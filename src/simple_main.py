#!/usr/bin/env python3
"""
简化版Google Maps Grid Search主程序

专注于核心功能，减少不必要的复杂性。
"""

import argparse
import time
import os
from typing import Dict, Any, List, Tuple

from src.config import create_parser, create_application_config, ApplicationConfig
from src.app_state import ApplicationState
from src.api_client import APIClient
from src.grid_algorithms import generate_grid_points
from src.data_models import create_search_orchestration_with_grid, SearchStatus
from src.state_manager import StateManager
from src.logging_config import configure_logging, get_logger
from src.exceptions import GridRuntimeError

logger = get_logger(__name__)


def setup_search_area(app_state: ApplicationState, api_client: APIClient) -> Tuple[float, float, float, float]:
    """设置搜索区域并返回边界"""
    search_config = app_state.get_search_config()
    
    if (search_config.test_area and 
        search_config.test_area != "all" and 
        search_config.test_area in search_config.test_areas):
        # 使用预定义的测试区域
        bounds = search_config.test_areas[search_config.test_area]["bounds"]
        area_name = search_config.test_areas[search_config.test_area]["name"]
        logger.info(f"使用测试区域: {area_name}")
    else:
        # 使用API客户端获取边界框
        bounds = api_client.get_bounding_box(search_config.location)
        if bounds is None:
            raise RuntimeError(f"无法获取位置 '{search_config.location}' 的边界框")
        logger.info(f"使用API获取的边界框: {bounds}")
    
    # 设置搜索边界
    app_state.get_search_state().set_search_bounds(bounds)
    logger.info(f"搜索区域边界: {bounds}")
    
    return bounds


def create_initial_grid(bounds: Tuple[float, float, float, float], 
                       initial_radius: float, 
                       initial_grid_step: float) -> List[Tuple[float, float]]:
    """创建初始网格"""
    logger.info(f"生成L0初始网格，步长: {initial_grid_step:.2f}m...")
    
    grid_points = generate_grid_points(
        bounds=bounds,
        step_meters=initial_grid_step
    )
    
    logger.info(f"生成了 {len(grid_points)} 个L0网格点")
    return grid_points


def process_grid_simple(orchestration, api_client: APIClient, app_state: ApplicationState) -> Dict[str, Any]:
    """简化的网格处理逻辑"""
    search_config = app_state.get_search_config()
    api_state = app_state.get_api_state()
    
    stats = {
        "total_cells": len(orchestration.cells),
        "processed_cells": 0,
        "api_calls": 0,
        "places_found": 0,
        "start_time": time.time()
    }
    
    logger.info(f"开始处理 {stats['total_cells']} 个网格单元...")
    
    for cell_id, cell in orchestration.cells.items():
        if api_state.api_call_count >= search_config.max_calls:
            logger.warning(f"达到最大API调用限制 ({search_config.max_calls})，停止处理")
            break
            
        try:
            # 执行搜索
            result = api_client.perform_nearby_search(
                lat=cell.center_lat,
                lng=cell.center_lng,
                radius=cell.search_radius,
                place_type=search_config.place_type,
                refine_level=0
            )
            
            stats["api_calls"] += 1
            api_state.increment_call_count()
            
            # 处理结果
            if result.get("status") == "OK" and "results" in result:
                places = result["results"]
                stats["places_found"] += len(places)

                # 提取地点ID
                place_ids = {place.get("place_id") for place in places if place.get("place_id")}

                # 更新单元状态
                cell.record_search_results(len(places), place_ids, 1)
                cell.update_status(SearchStatus.SEARCH_COMPLETE)
                logger.info(f"单元 {cell_id} 完成，找到 {len(places)} 个地点")
            else:
                cell.record_search_results(0, set(), 1)
                cell.update_status(SearchStatus.SEARCH_COMPLETE)
                logger.debug(f"单元 {cell_id} 无结果")
            
            stats["processed_cells"] += 1
            
            # 每处理10个单元输出一次进度
            if stats["processed_cells"] % 10 == 0:
                elapsed = time.time() - stats["start_time"]
                logger.info(f"已处理 {stats['processed_cells']}/{stats['total_cells']} 个单元，"
                          f"用时 {elapsed:.1f}s，找到 {stats['places_found']} 个地点")
                
        except Exception as e:
            logger.error(f"处理单元 {cell_id} 时出错: {e}")
            cell.set_error(str(e))
            continue
    
    stats["end_time"] = time.time()
    stats["total_time"] = stats["end_time"] - stats["start_time"]
    
    return stats


def generate_simple_report(orchestration, stats: Dict[str, Any], output_dir: str):
    """生成简化的报告"""
    # 收集所有地点ID
    all_place_ids = set()
    for cell in orchestration.cells.values():
        all_place_ids.update(cell.place_ids)
    
    # 保存地点ID
    import json
    output_file = os.path.join(output_dir, "found_place_ids.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(list(all_place_ids), f, indent=2, ensure_ascii=False)
    
    # 输出统计信息
    logger.info("\n=== 搜索完成统计 ===")
    logger.info(f"总运行时间: {stats['total_time']:.1f} 秒")
    logger.info(f"处理的单元数: {stats['processed_cells']}/{stats['total_cells']}")
    logger.info(f"API调用次数: {stats['api_calls']}")
    logger.info(f"找到的唯一地点数: {len(all_place_ids)}")
    logger.info(f"结果已保存到: {output_file}")
    logger.info("==================")


def main():
    """简化版主函数"""
    try:
        # 1. 解析命令行参数
        parser = create_parser()
        args = parser.parse_args()
        
        # 2. 配置日志
        log_level = getattr(args, 'log_level', 'INFO')
        configure_logging(log_level=log_level)
        
        logger.info("=== 简化版 Google Maps Grid Search 启动 ===")
        logger.info(f"工作目录: {args.work_dir}")
        
        # 3. 加载配置
        config = create_application_config(args)
        app_state = ApplicationState(config)
        app_state.log_summary()
        
        # 4. 创建API客户端
        api_client = APIClient(
            config=config,
            api_state=app_state.get_api_state(),
            search_config=app_state.get_search_config(),
            file_paths=app_state.get_file_paths(),
        )
        
        # 5. 设置搜索区域
        bounds = setup_search_area(app_state, api_client)
        
        # 6. 创建初始网格
        initial_radius = getattr(args, 'initial_radius', 50000.0)
        initial_grid_step = getattr(args, 'initial_grid_step', 40000.0)
        
        grid_points = create_initial_grid(bounds, initial_radius, initial_grid_step)
        
        # 7. 创建搜索编排对象
        orchestration = create_search_orchestration_with_grid(
            place_type=config.place_type,
            location=config.location,
            grid_points=grid_points,
            search_radius=initial_radius
        )
        
        # 8. 处理网格
        stats = process_grid_simple(orchestration, api_client, app_state)
        
        # 9. 生成报告
        output_dir = app_state.get_file_paths().get_output_dir()
        generate_simple_report(orchestration, stats, output_dir)
        
        logger.info("=== 简化版搜索完成 ===")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise


if __name__ == "__main__":
    main()
