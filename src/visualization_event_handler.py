"""
可视化事件处理器模块

为 Google Maps Grid Search 项目提供统一的可视化事件处理机制。
将事件处理逻辑从主程序中解耦，提供灵活的事件注册和处理接口。
"""

from typing import Optional, Any, Callable
from dataclasses import dataclass

from src.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class VisualizationEvent:
    """可视化事件数据类

    封装可视化相关的事件信息，提供统一的事件数据结构。
    """

    event_type: str
    cell: Optional[Any] = None
    orchestration: Optional[Any] = None
    timestamp: float = None
    metadata: dict = None

    def __post_init__(self):
        """初始化后处理"""
        import time

        if self.timestamp is None:
            self.timestamp = time.time()
        if self.metadata is None:
            self.metadata = {}


class VisualizationEventHandler:
    """可视化事件处理器类

    负责处理各种可视化相关的事件，包括：
    - 网格单元处理完成事件
    - 细化完成事件
    - 初始化完成事件

    提供统一的事件处理接口，支持灵活的事件注册和分发。
    """

    def __init__(self, visualization_service: Optional[Any] = None):
        """初始化事件处理器

        Args:
            visualization_service: 可视化服务实例，如果为None则事件处理将被跳过
        """
        self.visualization_service = visualization_service
        self.event_handlers = {}
        self.enabled = visualization_service is not None

        # 注册默认事件处理器
        self._register_default_handlers()

        logger.info(
            f"可视化事件处理器初始化完成，状态: {'启用' if self.enabled else '禁用'}"
        )

    def _register_default_handlers(self):
        """注册默认的事件处理器"""
        if not self.enabled:
            return

        self.event_handlers.update(
            {
                "cell_processed": self._handle_cell_processed,
                "refinement_complete": self._handle_refinement_complete,
                "initialization": self._handle_initialization,
                "error": self._handle_error,
            }
        )

    def register_handler(
        self, event_type: str, handler: Callable[[VisualizationEvent], None]
    ):
        """注册自定义事件处理器

        Args:
            event_type: 事件类型
            handler: 事件处理函数，接收VisualizationEvent参数

        Example:
            >>> def custom_handler(event: VisualizationEvent):
            ...     print(f"事件处理: {event.event_type}")
            >>> handler.register_handler("custom_event", custom_handler)
        """
        self.event_handlers[event_type] = handler
        logger.debug(f"已注册事件处理器: {event_type}")

    def unregister_handler(self, event_type: str):
        """注销事件处理器

        Args:
            event_type: 要注销的事件类型
        """
        if event_type in self.event_handlers:
            del self.event_handlers[event_type]
            logger.debug(f"已注销事件处理器: {event_type}")

    def handle_event(
        self,
        event_type: str,
        cell: Optional[Any] = None,
        orchestration: Optional[Any] = None,
        **kwargs,
    ) -> bool:
        """处理可视化事件

        这是主要的事件处理入口点，由外部调用。

        Args:
            event_type: 事件类型
            cell: 相关的网格单元对象
            orchestration: 搜索编排对象
            **kwargs: 额外的元数据

        Returns:
            bool: 事件处理是否成功

        Raises:
            ValueError: 当事件类型无效时抛出
        """
        if not self.enabled:
            logger.debug(f"可视化服务未启用，跳过事件: {event_type}")
            return False

        # 验证事件类型
        if not event_type or not isinstance(event_type, str):
            logger.warning(f"无效的事件类型: {event_type}")
            return False

        # 创建事件对象
        event = VisualizationEvent(
            event_type=event_type,
            cell=cell,
            orchestration=orchestration,
            metadata=kwargs,
        )

        # 查找并执行事件处理器
        handler = self.event_handlers.get(event_type)
        if handler:
            try:
                handler(event)
                logger.debug(f"事件处理成功: {event_type}")
                return True
            except Exception as e:
                logger.error(f"事件处理失败: {event_type} - {e}")
                # 尝试调用错误处理器
                self._handle_error(event, error=e)
                return False
        else:
            logger.warning(f"未找到事件处理器: {event_type}")
            return False

    def handle_callback(
        self,
        event_type: str,
        cell: Optional[Any] = None,
        orchestration: Optional[Any] = None,
    ) -> bool:
        """兼容性方法：处理回调事件

        这个方法提供了与现有回调系统的兼容性。

        Args:
            event_type: 事件类型
            cell: 相关的网格单元对象
            orchestration: 搜索编排对象

        Returns:
            bool: 回调处理是否成功
        """
        return self.handle_event(event_type, cell, orchestration)

    # 默认事件处理器方法
    def _handle_cell_processed(self, event: VisualizationEvent):
        """处理网格单元处理完成事件"""
        if self.visualization_service and event.cell and event.orchestration:
            try:
                self.visualization_service.update_on_cell_processed(
                    event.cell,
                    event.orchestration,
                    force=event.metadata.get("force", False),
                )
                logger.debug(
                    f"单元格处理可视化更新: {event.cell.cell_id if hasattr(event.cell, 'cell_id') else 'Unknown'}"
                )
            except Exception as e:
                logger.error(f"单元格处理可视化更新失败: {e}")
                raise  # 重新抛出异常，让handle_event方法能够捕获并返回False

    def _handle_refinement_complete(self, event: VisualizationEvent):
        """处理细化完成事件"""
        if self.visualization_service and event.orchestration:
            try:
                self.visualization_service.update_on_refinement(event.orchestration)
                logger.debug("细化完成可视化更新")
            except Exception as e:
                logger.error(f"细化可视化更新失败: {e}")
                raise  # 重新抛出异常，让handle_event方法能够捕获并返回False

    def _handle_initialization(self, event: VisualizationEvent):
        """处理初始化完成事件"""
        if self.visualization_service and event.orchestration:
            try:
                self.visualization_service.update_on_initialization(event.orchestration)
                logger.debug("初始化可视化更新")
            except Exception as e:
                logger.error(f"初始化可视化更新失败: {e}")
                raise  # 重新抛出异常，让handle_event方法能够捕获并返回False

    def _handle_error(
        self, event: VisualizationEvent, error: Optional[Exception] = None
    ):
        """处理错误事件"""
        error_msg = f"可视化事件处理错误: {event.event_type}"
        if error:
            error_msg += f" - {error}"

        logger.error(error_msg)

        # 如果有可视化服务，尝试记录错误状态
        if self.visualization_service and hasattr(
            self.visualization_service, "record_error"
        ):
            try:
                self.visualization_service.record_error(error_msg)
            except Exception:
                pass  # 避免错误处理中的错误

    def get_status(self) -> dict:
        """获取事件处理器状态信息

        Returns:
            dict: 包含处理器状态的字典
        """
        return {
            "enabled": self.enabled,
            "registered_handlers": list(self.event_handlers.keys()),
            "service_available": self.visualization_service is not None,
        }

    def enable(self):
        """启用事件处理器"""
        if self.visualization_service:
            self.enabled = True
            logger.info("可视化事件处理器已启用")
        else:
            logger.warning("无法启用事件处理器：可视化服务不可用")

    def disable(self):
        """禁用事件处理器"""
        self.enabled = False
        logger.info("可视化事件处理器已禁用")

    def reset(self):
        """重置事件处理器状态"""
        self.event_handlers.clear()
        self._register_default_handlers()
        logger.debug("事件处理器已重置")
