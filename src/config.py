"""
统一配置管理模块

重构后的配置系统，合并所有配置类为单一Config类。
简化配置管理，提供更直观的接口。
"""

import os
import json
import argparse
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, Set
from dotenv import load_dotenv
from pathlib import Path

from src.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class Config:
    """统一配置类 - 合并所有配置到单一类中"""
    
    # === 基础配置 ===
    api_key: str
    place_type: str = "restaurant"
    location: str = "Berlin, Germany"
    work_dir: str = "."
    log_level: str = "INFO"
    
    # === 搜索配置 ===
    max_calls: int = 4000
    dry_run: bool = False
    visualize: bool = False
    mock_seed: int = 42
    
    # === 测试区域配置 ===
    test_area: Optional[str] = None
    test_areas: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # === 算法配置 ===
    max_radius: int = 50000
    min_refinement_radius: int = 250
    max_refinement_levels: int = 4
    subdivision_threshold: int = 20
    initial_radius_factor: float = 0.25
    grid_step_factor: float = 0.8
    
    # === 用户指定参数 ===
    initial_radius: Optional[float] = None
    initial_grid_step: Optional[float] = None
    
    # === API配置 ===
    nearby_search_single_page_size: int = 20
    max_api_calls_per_cell: int = 3
    page_delay_seconds: float = 2.0
    results_threshold_for_refinement: int = 20
    
    # === 运行时状态 ===
    api_call_count: int = 0
    found_places: Set[str] = field(default_factory=set)
    search_bounds: Optional[tuple] = None
    
    # === 文件路径 ===
    output_dir: str = field(init=False)
    progress_file: str = field(init=False)
    visualization_file: Optional[str] = field(init=False)
    
    def __post_init__(self):
        """初始化后处理"""
        # 设置默认测试区域
        if not self.test_areas:
            self.test_areas = {
                "alexanderplatz": {
                    "name": "Alexanderplatz (Dense)",
                    "bounds": (52.5150, 13.4050, 52.5250, 13.4150),
                },
                "tiergarten": {
                    "name": "Tiergarten (Sparse)",
                    "bounds": (52.5100, 13.3500, 52.5200, 13.3700),
                },
                "kreuzberg": {
                    "name": "Kreuzberg (Medium)",
                    "bounds": (52.4950, 13.3800, 52.5050, 13.4100),
                },
                "friedrichstrasse": {
                    "name": "Friedrichstraße Area",
                    "bounds": (52.5000, 13.3850, 52.5300, 13.3950),
                },
                "losangeles": {
                    "name": "Los Angeles 60km",
                    "bounds": (
                        33.10020802500898,
                        -119.21747347409674,
                        34.89683037499101,
                        -117.05039312590326,
                    ),
                },
            }
        
        # 设置文件路径
        self._setup_file_paths()
        
        # 验证配置
        self._validate_config()
    
    def _setup_file_paths(self):
        """设置文件路径"""
        if self.work_dir == "." or self.work_dir == "":
            self.output_dir = os.path.join("output", "default")
        else:
            self.output_dir = os.path.join("output", self.work_dir)
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 设置具体文件路径
        self.progress_file = os.path.join(self.output_dir, "orchestration.json")
        
        if self.visualize:
            self.visualization_file = os.path.join(self.output_dir, "visualization_map.html")
        else:
            self.visualization_file = None
    
    def _validate_config(self):
        """验证配置参数"""
        if not self.place_type:
            raise ValueError("place_type cannot be empty")
        
        if not self.location:
            raise ValueError("location cannot be empty")
        
        if self.max_calls < 0:
            raise ValueError("max_calls must be non-negative")
        
        if self.test_area and self.test_area not in self.test_areas:
            available_areas = list(self.test_areas.keys())
            raise ValueError(f"Unknown test area: {self.test_area}. Available: {available_areas}")
    
    def get_search_bounds(self):
        """获取搜索边界"""
        if (self.test_area and 
            self.test_area != "all" and 
            self.test_area in self.test_areas):
            return self.test_areas[self.test_area]["bounds"]
        return self.search_bounds
    
    def get_location_name(self):
        """获取位置名称（用于显示）"""
        if (self.test_area and 
            self.test_area != "all" and 
            self.test_area in self.test_areas):
            return f"{self.test_areas[self.test_area]['name']} (测试区域: {self.test_area})"
        return self.location
    
    def increment_api_calls(self):
        """增加API调用计数"""
        self.api_call_count += 1
    
    def add_found_places(self, place_ids: Set[str]):
        """添加发现的地点"""
        self.found_places.update(place_ids)
    
    def log_summary(self):
        """记录配置摘要"""
        logger.info("=== 配置摘要 ===")
        logger.info(f"工作目录: {self.work_dir}")
        logger.info(f"搜索地点类型: {self.place_type}")
        logger.info(f"目标位置: {self.get_location_name()}")
        logger.info(f"试运行模式: {self.dry_run}")
        logger.info(f"可视化模式: {self.visualize}")
        logger.info(f"最大API调用次数: {self.max_calls}")
        logger.info(f"已发现地点数量: {len(self.found_places)}")
        logger.info(f"API调用次数: {self.api_call_count}")
        logger.info("===============")


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="Extract place data from Google Maps API using grid-based search"
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="Run in dry run mode with mock responses"
    )
    parser.add_argument(
        "--test-area",
        choices=[
            "alexanderplatz",
            "tiergarten", 
            "kreuzberg",
            "friedrichstrasse",
            "all",
            "losangeles",
        ],
        help="Run on specific test area instead of full Berlin",
    )
    parser.add_argument(
        "--place-type",
        default="restaurant",
        help="Type of place to search for (default: restaurant)",
    )
    parser.add_argument(
        "--location",
        default="Berlin, Germany",
        help="Location to search in (default: Berlin, Germany)",
    )
    parser.add_argument(
        "--work-dir",
        default=".",
        help="Working directory for output files (default: current directory)",
    )
    parser.add_argument(
        "--max-calls",
        type=int,
        default=4000,
        help="Maximum number of API calls (default: 4000)",
    )
    parser.add_argument(
        "--visualize",
        action="store_true",
        help="Enable real-time visualization",
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Set the logging level (default: INFO)",
    )
    parser.add_argument(
        "--mock-seed",
        type=int,
        default=42,
        help="Random seed for mock data generation (default: 42)",
    )
    parser.add_argument(
        "--initial-radius",
        type=float,
        help="Initial search radius in meters",
    )
    parser.add_argument(
        "--initial-grid-step",
        type=float,
        help="Initial grid step size in meters",
    )
    
    return parser


def load_algorithm_config():
    """加载算法配置文件"""
    config_file = "algorithm_config.json"
    
    if not os.path.exists(config_file):
        logger.warning(f"算法配置文件 {config_file} 不存在，使用默认配置")
        return {
            "search": {
                "max_radius": 50000,
                "min_refinement_radius": 250,
                "max_refinement_levels": 4,
                "subdivision_threshold": 20,
                "initial_radius_factor": 0.25,
                "grid_step_factor": 0.8,
            },
            "api": {
                "nearby_search_single_page_size": 20,
                "max_api_calls_per_cell": 3,
                "page_delay_seconds": 2.0,
                "results_threshold_for_refinement": 20,
            }
        }
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载算法配置文件失败: {e}")
        raise


def create_config(args=None) -> Config:
    """创建统一配置对象"""
    # 加载环境变量
    load_dotenv()
    
    # 解析命令行参数
    if args is None:
        parser = create_parser()
        args = parser.parse_args()
    
    # 获取API密钥
    api_key = os.getenv("GOOGLE_MAPS_API_KEY", "")
    if not api_key and not args.dry_run:
        raise ValueError("GOOGLE_MAPS_API_KEY environment variable is required for real API calls")
    
    # 加载算法配置
    algorithm_config = load_algorithm_config()
    search_config = algorithm_config.get("search", {})
    api_config = algorithm_config.get("api", {})
    
    # 创建配置对象
    config = Config(
        api_key=api_key,
        place_type=args.place_type,
        location=args.location,
        work_dir=args.work_dir,
        log_level=args.log_level,
        max_calls=args.max_calls,
        dry_run=args.dry_run,
        visualize=args.visualize,
        mock_seed=args.mock_seed,
        test_area=args.test_area,
        initial_radius=args.initial_radius,
        initial_grid_step=args.initial_grid_step,
        
        # 算法配置
        max_radius=search_config.get("max_radius", 50000),
        min_refinement_radius=search_config.get("min_refinement_radius", 250),
        max_refinement_levels=search_config.get("max_refinement_levels", 4),
        subdivision_threshold=search_config.get("subdivision_threshold", 20),
        initial_radius_factor=search_config.get("initial_radius_factor", 0.25),
        grid_step_factor=search_config.get("grid_step_factor", 0.8),
        
        # API配置
        nearby_search_single_page_size=api_config.get("nearby_search_single_page_size", 20),
        max_api_calls_per_cell=api_config.get("max_api_calls_per_cell", 3),
        page_delay_seconds=api_config.get("page_delay_seconds", 2.0),
        results_threshold_for_refinement=api_config.get("results_threshold_for_refinement", 20),
    )
    
    return config
