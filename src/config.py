import os
import argparse
import json
from dotenv import load_dotenv
from dataclasses import dataclass, field
from typing import Optional, Dict, Any

# --- 命令行参数 ---


def create_parser():
    """Create and return the command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Extract place data from Google Maps API using grid-based search"
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="Run in dry run mode with mock responses"
    )
    parser.add_argument(
        "--test-area",
        choices=[
            "alexanderplatz",
            "tiergarten",
            "kreuzberg",
            "friedrichstrasse",
            "all",
            "losangeles",
        ],
        help="Run on specific test area instead of full Berlin",
    )
    parser.add_argument(
        "--mock-seed",
        type=int,
        default=42,
        help="Random seed for mock data generation in dry run mode",
    )
    parser.add_argument(
        "--max-calls",
        type=int,
        default=4000,
        help="Maximum API calls to make before stopping (0 = unlimited)",
    )
    parser.add_argument(
        "--visualize",
        action="store_true",
        help="Generate visualization maps of the search",
    )
    parser.add_argument(
        "--param-test", action="store_true", help="Run parameter sensitivity testing"
    )
    parser.add_argument(
        "--place-type",
        type=str,
        help="The type of place to search for (e.g., restaurant, cafe, gym)",
    )
    parser.add_argument(
        "--location",
        type=str,
        help="The location to search in (e.g., 'New York, NY', 'London, UK')",
    )
    parser.add_argument(
        "--work-dir",
        type=str,
        default=".",
        help="Working directory for output files (default: current directory)",
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default="INFO",
        help="Set the logging level (default: INFO)",
    )
    parser.add_argument(
        "--initial-radius",
        type=float,
        help="Initial search radius in meters for grid generation (default: auto-calculated)",
    )
    parser.add_argument(
        "--initial-grid-step",
        type=float,
        help="Initial grid step size in meters for grid generation (default: auto-calculated)",
    )
    return parser


# --- 算法配置 ---
def load_algorithm_config():
    """加载算法配置文件并返回配置字典。"""
    config_file = "algorithm_config.json"

    # 检查配置文件是否存在
    if not os.path.exists(config_file):
        raise FileNotFoundError(
            f"算法配置文件 {config_file} 不存在。请创建配置文件或从模板复制。\n"
            f"必需的配置结构：\n"
            f"{{\n"
            f'  "search": {{\n'
            f'    "max_radius": 50000,\n'
            f'    "subdivision_threshold": 45,\n'
            f'    "mini_radius_factor": 3.0,\n'
            f'    "mini_grid_overlap_factor": 1.0,\n'
            f'    "max_refinement_levels": 4,\n'
            f'    "min_refinement_radius": 250,\n'
            f'    "initial_radius_factor": 0.25,\n'
            f'    "grid_step_factor": 0.8\n'
            f"  }},\n"
            f'  "api": {{\n'
            f'    "nearby_search_single_page_size": 20,\n'
            f'    "nearby_search_actual_limit": 60\n'
            f"  }}\n"
            f"}}"
        )

    # 加载配置文件
    try:
        with open(config_file, "r", encoding="utf-8") as f:
            config = json.load(f)
    except json.JSONDecodeError as e:
        raise ValueError(f"算法配置文件 {config_file} 格式错误：{e}")
    except IOError as e:
        raise IOError(f"无法读取算法配置文件 {config_file}：{e}")

    # 验证配置结构
    required_sections = ["search", "api"]
    for section in required_sections:
        if section not in config:
            raise ValueError(f"配置文件缺少必需的节：{section}")

    # 验证 search 节的必需字段
    required_search_fields = [
        "max_radius",
        "subdivision_threshold",
        "mini_radius_factor",
        "mini_grid_overlap_factor",
        "max_refinement_levels",
        "min_refinement_radius",
        "initial_radius_factor",
        "grid_step_factor",
    ]
    for field in required_search_fields:
        if field not in config["search"]:
            raise ValueError(f"配置文件 search 节缺少必需字段：{field}")

    # 验证 api 节的必需字段
    required_api_fields = [
        "nearby_search_single_page_size",
        "nearby_search_actual_limit",
    ]
    for field in required_api_fields:
        if field not in config["api"]:
            raise ValueError(f"配置文件 api 节缺少必需字段：{field}")

    # 验证参数值的合理性
    search_config = config["search"]
    if (
        not isinstance(search_config["max_radius"], int)
        or search_config["max_radius"] <= 0
    ):
        raise ValueError("max_radius 必须是正整数")
    if (
        not isinstance(search_config["subdivision_threshold"], int)
        or search_config["subdivision_threshold"] <= 0
    ):
        raise ValueError("subdivision_threshold 必须是正整数")
    if (
        not isinstance(search_config["mini_radius_factor"], (int, float))
        or search_config["mini_radius_factor"] <= 0
    ):
        raise ValueError("mini_radius_factor 必须是正数")
    if (
        not isinstance(search_config["mini_grid_overlap_factor"], (int, float))
        or search_config["mini_grid_overlap_factor"] <= 0
    ):
        raise ValueError("mini_grid_overlap_factor 必须是正数")
    if (
        not isinstance(search_config["max_refinement_levels"], int)
        or search_config["max_refinement_levels"] < 0
    ):
        raise ValueError("max_refinement_levels 必须是非负整数")
    if (
        not isinstance(search_config["min_refinement_radius"], int)
        or search_config["min_refinement_radius"] <= 0
    ):
        raise ValueError("min_refinement_radius 必须是正整数")
    if (
        not isinstance(search_config["initial_radius_factor"], (int, float))
        or search_config["initial_radius_factor"] <= 0
    ):
        raise ValueError("initial_radius_factor 必须是正数")
    if (
        not isinstance(search_config["grid_step_factor"], (int, float))
        or search_config["grid_step_factor"] <= 0
    ):
        raise ValueError("grid_step_factor 必须是正数")

    api_config = config["api"]
    if (
        not isinstance(api_config["nearby_search_single_page_size"], int)
        or api_config["nearby_search_single_page_size"] <= 0
    ):
        raise ValueError("nearby_search_single_page_size 必须是正整数")
    if (
        not isinstance(api_config["nearby_search_actual_limit"], int)
        or api_config["nearby_search_actual_limit"] <= 0
    ):
        raise ValueError("nearby_search_actual_limit 必须是正整数")

    return config


# --- 配置类 ---
# Removed deprecated VisualizationConfig and AppConfig classes


@dataclass
class ApplicationConfig:
    """简化的应用配置类
    
    统一管理所有配置参数，提供简单的验证方法。
    """
    
    # 基本配置
    api_key: str
    place_type: str = "restaurant"
    location: str = "Berlin, Germany"
    work_dir: str = "."
    log_level: str = "INFO"
    max_calls: int = 4000
    dry_run: bool = False
    visualize: bool = False
    mock_seed: int = 42
    
    # 高级配置
    test_area: Optional[str] = None
    param_test: bool = False
    initial_radius: Optional[float] = None
    initial_grid_step: Optional[float] = None
    
    # 算法配置（从 algorithm_config.json 加载）
    max_radius: int = 50000
    nearby_search_single_page_size: int = 20
    nearby_search_actual_limit: int = 60
    subdivision_threshold: int = 45
    mini_radius_factor: float = 3.0
    mini_grid_overlap_factor: float = 1.0
    max_refinement_levels: int = 4
    min_refinement_radius: int = 250
    initial_radius_factor: float = 0.25
    grid_step_factor: float = 0.8
    
    # 测试区域配置
    test_areas: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        # 设置默认测试区域
        if not self.test_areas:
            self.test_areas = {
                "alexanderplatz": {
                    "name": "Alexanderplatz (Dense)",
                    "bounds": (52.5150, 13.4050, 52.5250, 13.4150),
                },
                "tiergarten": {
                    "name": "Tiergarten (Sparse)",
                    "bounds": (52.5100, 13.3500, 52.5200, 13.3600),
                },
                "kreuzberg": {
                    "name": "Kreuzberg (Mixed)",
                    "bounds": (52.4900, 13.3900, 52.5000, 13.4000),
                },
                "friedrichstrasse": {
                    "name": "Friedrichstraße Area",
                    "bounds": (52.5000, 13.3850, 52.5300, 13.3950),
                },
                "losangeles": {
                    "name": "Los Angeles 60km",
                    "bounds": (
                        33.10020802500898,
                        -119.21747347409674,
                        34.89683037499101,
                        -117.05039312590326,
                    ),
                },
            }
        
        # 简单验证
        self._validate_basic()
    
    def _validate_basic(self):
        """基本配置验证"""
        if not self.api_key:
            raise ValueError("API key is required")
        
        if not self.place_type:
            raise ValueError("place_type cannot be empty")
        
        if not self.location:
            raise ValueError("location cannot be empty")
        
        if self.max_calls < 0:
            raise ValueError("max_calls must be non-negative")
        
        if self.test_area and self.test_area not in self.test_areas:
            available_areas = list(self.test_areas.keys())
            raise ValueError(f"Unknown test area: {self.test_area}. Available: {available_areas}")
    
    @classmethod
    def from_args(cls, args, api_key: str, algorithm_config: dict) -> 'ApplicationConfig':
        """从命令行参数创建配置对象"""
        # 从算法配置中提取参数
        search_config = algorithm_config.get("search", {})
        api_config = algorithm_config.get("api", {})
        
        return cls(
            api_key=api_key,
            place_type=args.place_type if args.place_type is not None else "restaurant",
            location=args.location if args.location is not None else "Berlin, Germany",
            work_dir=args.work_dir or ".",
            log_level=args.log_level or "INFO",
            max_calls=args.max_calls or 4000,
            dry_run=args.dry_run or False,
            visualize=args.visualize or False,
            mock_seed=args.mock_seed or 42,
            test_area=args.test_area,
            param_test=args.param_test or False,
            initial_radius=getattr(args, 'initial_radius', None),
            initial_grid_step=getattr(args, 'initial_grid_step', None),
            max_radius=search_config.get("max_radius", 50000),
            nearby_search_single_page_size=api_config.get("nearby_search_single_page_size", 20),
            nearby_search_actual_limit=api_config.get("nearby_search_actual_limit", 60),
            subdivision_threshold=search_config.get("subdivision_threshold", 45),
            mini_radius_factor=search_config.get("mini_radius_factor", 3.0),
            mini_grid_overlap_factor=search_config.get("mini_grid_overlap_factor", 1.0),
            max_refinement_levels=search_config.get("max_refinement_levels", 4),
            min_refinement_radius=search_config.get("min_refinement_radius", 250),
            initial_radius_factor=search_config.get("initial_radius_factor", 0.25),
            grid_step_factor=search_config.get("grid_step_factor", 0.8),
        )


def create_application_config(args=None) -> ApplicationConfig:
    """创建应用配置对象
    
    从环境变量、算法配置文件和命令行参数创建统一的应用配置对象。
    """
    load_dotenv()
    api_key = os.getenv("GOOGLE_MAPS_API_KEY")
    if not api_key:
        raise ValueError(
            "Google Maps API key not found. Please set GOOGLE_MAPS_API_KEY environment variable."
        )

    # 加载算法配置
    algorithm_config = load_algorithm_config()

    # 解析命令行参数（如果未提供）
    if args is None:
        import sys

        # 检查是否在测试环境中（pytest参数存在）
        pytest_args = {
            "-v",
            "--tb=short",
            "--tb=long",
            "-s",
            "--collect-only",
            "tests/",
        }
        if any(arg in sys.argv for arg in pytest_args):
            # 在测试环境中，使用默认配置
            class MockArgs:
                def __init__(self):
                    self.place_type = None
                    self.location = None
                    self.work_dir = "."
                    self.log_level = "INFO"
                    self.max_calls = 4000
                    self.dry_run = False
                    self.visualize = False
                    self.mock_seed = 42
                    self.test_area = None
                    self.param_test = False
                    self.initial_radius = None
                    self.initial_grid_step = None

            args = MockArgs()
        else:
            parser = create_parser()
            args = parser.parse_args()

    # 使用 ApplicationConfig.from_args 创建配置对象
    return ApplicationConfig.from_args(args, api_key, algorithm_config)


# Removed deprecated load_config and validate_config functions
