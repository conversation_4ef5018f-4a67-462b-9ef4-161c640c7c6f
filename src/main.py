"""
主程序模块

Google Maps Grid Search 项目的入口点。
使用新的数据驱动架构重构，基于 StateManager 和 GridEngine。

基于 TECHNICAL_DESIGN.md 中的设计规范。
"""

import os
import time
import traceback
from typing import Optional

# 导入新架构的模块
from src.config import create_parser, create_application_config
from src.grid_algorithms import generate_grid_points, calculate_search_parameters
from src.data_models import (
    SearchOrchestration,
    SearchStatus,
    create_search_orchestration_with_grid,
)
from src.state_manager import StateManager, create_state_manager
from src.grid_engine import GridEngine, ProcessingConfig
from src.api_client import APIClient
from src.app_state import ApplicationState
from src.report_generator import generate_final_report
from src.visualization import (
    generate_visualization_from_orchestration,
    save_map_data,
)
from src.logging_config import get_logger, configure_logging
from src.exceptions import GridRuntimeError

# 获取日志记录器
logger = get_logger(__name__)


def _setup_search_area(
    app_state: ApplicationState, api_client: APIClient
) -> Optional[tuple]:
    """设置搜索区域并返回边界。

    Args:
        app_state: 应用程序状态
        api_client: API客户端

    Returns:
        边界元组 (min_lat, min_lng, max_lat, max_lng) 或 None（失败时）

    Raises:
        RuntimeError: 获取边界框失败时抛出
    """
    search_config = app_state.get_search_config()
    search_state = app_state.get_search_state()

    try:
        # 根据模式获取适当的边界
        if (
            search_config.test_area
            and search_config.test_area != "all"
            and search_config.test_area in search_config.test_areas
        ):
            # 使用预定义的测试区域
            bounds = search_config.test_areas[search_config.test_area]["bounds"]
            logger.info(
                f"使用测试区域: {search_config.test_areas[search_config.test_area]['name']}"
            )
        else:
            # 使用API客户端获取边界框
            bounds = api_client.get_bounding_box(search_config.location)
            if bounds is None:
                error_msg = "获取边界框失败"
                logger.error(error_msg)
                raise GridRuntimeError(error_msg)

        logger.info(f"搜索区域边界: {bounds}")
        search_state.set_search_bounds(bounds)
        return bounds

    except Exception as e:
        error_msg = f"获取边界框失败: {e}"
        logger.error(error_msg)
        raise GridRuntimeError(error_msg) from e


def _create_or_load_orchestration(
    state_manager: StateManager, search_config, bounds: tuple, app_state=None
) -> SearchOrchestration:
    """创建或加载搜索编排对象。

    Args:
        state_manager: 状态管理器
        search_config: 搜索配置
        bounds: 搜索边界
        app_state: 应用程序状态（可选，用于可视化）

    Returns:
        SearchOrchestration: 搜索编排对象

    Raises:
        RuntimeError: 创建或加载编排对象失败时抛出
    """
    try:
        # 尝试加载现有的编排对象
        orchestration = state_manager.load_state_with_fallback()

        if orchestration is not None:
            logger.info("从现有状态加载搜索编排对象")
            return orchestration

        # 如果没有现有状态，创建新的编排对象
        logger.info("创建新的搜索编排对象")

        # 确定初始半径和网格步长
        if (
            search_config.initial_radius is not None
            and search_config.initial_grid_step is not None
        ):
            # 用户指定了参数，直接使用
            initial_radius = search_config.initial_radius
            initial_grid_step = search_config.initial_grid_step
            logger.info(
                f"使用用户指定的初始参数: 半径={initial_radius}m, 步长={initial_grid_step}m"
            )
        else:
            # 自动计算初始参数
            initial_radius, initial_grid_step, refinement_radius_factors = (
                calculate_search_parameters(
                    bounds,
                    search_config.max_radius,
                    search_config.min_refinement_radius,
                    search_config.max_refinement_levels,
                    search_config.initial_radius_factor,
                    search_config.grid_step_factor,
                )
            )
            logger.info(
                f"使用自动计算的初始参数: 半径={initial_radius}m, 步长={initial_grid_step}m"
            )

        # 生成L0初始网格
        logger.info(f"生成L0初始网格，步长: {initial_grid_step:.2f}m...")
        grid_points = generate_grid_points(bounds, initial_grid_step)
        logger.info(f"生成了 {len(grid_points)} 个L0网格点")

        # 创建编排对象并包含完整的L0网格
        config = {
            "max_refinement_levels": search_config.max_refinement_levels,
            "initial_radius": initial_radius,
            "min_refinement_radius": search_config.min_refinement_radius,
            "results_threshold_for_refinement": search_config.subdivision_threshold,
            "grid_overlap_factor": search_config.grid_step_factor,
        }

        orchestration = create_search_orchestration_with_grid(
            place_type=search_config.place_type,
            location=search_config.location,
            grid_points=grid_points,
            search_radius=initial_radius,
            config=config,
        )

        # 立即保存初始网格状态，确保数据不会丢失
        state_manager.save_state(orchestration)
        logger.info("初始网格已生成并保存")

        # 生成初始化可视化
        try:
            # 使用工厂创建可视化服务
            from src.visualization_factory import VisualizationFactory

            # 确保工作目录有效
            progress_dir = app_state.get_file_paths().get_progress_dir()
            viz_service = VisualizationFactory.create_service(progress_dir)

            if viz_service:
                viz_service.update_on_initialization(orchestration)
                logger.info(
                    f"初始化网格可视化已生成: {progress_dir}/visualization_map_initial.html"
                )
            else:
                logger.info("可视化服务不可用，跳过初始化可视化")

        except Exception as e:
            logger.warning(f"初始化可视化生成失败: {e}")
            # 不抛出异常，继续执行

        return orchestration

    except Exception as e:
        error_msg = f"创建或加载搜索编排对象失败: {e}"
        logger.error(error_msg)
        raise GridRuntimeError(error_msg) from e


def _create_grid_engine(
    orchestration: SearchOrchestration,
    api_client: APIClient,
    state_manager: StateManager,
    search_config,
    visualization_callback=None,
) -> GridEngine:
    """创建网格处理引擎。

    Args:
        orchestration: 搜索编排对象
        api_client: API客户端
        state_manager: 状态管理器
        search_config: 搜索配置
        visualization_callback: 可视化回调函数

    Returns:
        GridEngine: 网格处理引擎

    Raises:
        RuntimeError: 创建网格引擎失败时抛出
    """
    try:
        # 创建处理配置
        processing_config = ProcessingConfig(
            max_refinement_levels=search_config.max_refinement_levels,
            initial_radius=orchestration.config.get("initial_radius", 1000.0),
            min_refinement_radius=search_config.min_refinement_radius,
            max_api_calls_per_cell=3,
            results_threshold_for_refinement=search_config.subdivision_threshold,
            grid_overlap_factor=search_config.grid_step_factor,
        )

        # 创建网格处理引擎
        grid_engine = GridEngine(
            orchestration=orchestration,
            api_client=api_client,
            state_manager=state_manager,
            config=processing_config,
            visualization_callback=visualization_callback,
        )

        logger.info("网格处理引擎创建完成")
        return grid_engine

    except Exception as e:
        error_msg = f"创建网格处理引擎失败: {e}"
        logger.error(error_msg)
        raise GridRuntimeError(error_msg) from e


def _run_search_loop(grid_engine: GridEngine, search_config, api_state) -> tuple:
    """执行主搜索循环。

    Args:
        grid_engine: 网格处理引擎
        search_config: 搜索配置
        api_state: API状态

    Returns:
        tuple: (开始时间, 总API调用次数)

    Raises:
        RuntimeError: 搜索过程中出现严重错误时抛出
    """
    start_time = time.time()

    try:
        logger.info("开始主搜索循环...")

        # 持续处理层直到完成
        while grid_engine.has_pending_work():
            # 检查API调用限制
            if (
                search_config.max_calls > 0
                and api_state.api_call_count >= search_config.max_calls
            ):
                logger.warning(
                    f"已达到最大API调用限制 {search_config.max_calls}。停止。"
                )
                break

            # 处理下一层（状态保存已在 GridEngine 内部处理）
            has_more_layers, stats = grid_engine.process_next_layer()

            # 打印当前统计信息
            elapsed = time.time() - start_time
            logger.info(f"\n当前统计信息:")
            logger.info(f"  - 运行时间: {elapsed:.1f} 秒")
            logger.info(f"  - API调用次数: {api_state.api_call_count}")
            logger.info(
                f"  - 找到的地点数: {len(grid_engine.orchestration.get_all_place_ids())}"
            )
            logger.info(f"  - 处理的单元数: {stats.processed_cells}")
            logger.info(f"  - 当前层级: {grid_engine.get_current_layer()}")

            # 对Google的API友好 - 在层之间等待
            time.sleep(1)

            # 如果没有更多层需要处理，跳出循环
            if not has_more_layers:
                break

        logger.info("主搜索循环完成")
        return start_time, api_state.api_call_count

    except Exception as e:
        error_msg = f"主处理循环中出错: {e}"
        logger.error(f"*** {error_msg} ***")
        logger.error("搜索失败 - 需要人工介入检查错误并恢复")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise GridRuntimeError(error_msg) from e


def _generate_visualization_and_report(
    app_state: ApplicationState,
    orchestration: SearchOrchestration,
    api_state,
    start_time: float,
    total_api_calls: int,
):
    """生成可视化和最终报告。

    Args:
        app_state: 应用程序状态
        orchestration: 搜索编排对象
        api_state: API状态
        start_time: 开始时间
        total_api_calls: 总API调用次数

    Raises:
        RuntimeError: 生成可视化或报告失败时抛出
    """
    search_config = app_state.get_search_config()
    visualization_state = app_state.get_visualization_state()
    file_paths = app_state.get_file_paths()

    try:
        # 如果请求且我们有数据则生成可视化
        if search_config.visualize:
            try:
                logger.info("生成可视化...")
                generate_visualization_from_orchestration(
                    orchestration=orchestration,
                    output_file=file_paths.real_time_visualization_file,
                    search_bounds=app_state.get_search_state().search_bounds,
                )
                logger.info(f"可视化已保存到 {file_paths.real_time_visualization_file}")

                # 保存地图数据以供将来合并
                all_grid_points = [
                    (cell.center_lat, cell.center_lng)
                    for cell in orchestration.cells.values()
                ]
                all_refinement_points = [
                    (cell.center_lat, cell.center_lng)
                    for cell in orchestration.cells.values()
                    if cell.needs_refinement()
                    or cell.status == SearchStatus.REFINEMENT_COMPLETE
                ]

                save_map_data(
                    all_grid_points,
                    all_refinement_points,
                    visualization_state.place_coords_for_viz,
                    file_paths.map_data_file,
                )
                logger.info(f"地图数据已保存到 {file_paths.map_data_file}")

            except Exception as viz_error:
                error_msg = f"生成可视化失败: {viz_error}"
                logger.error(error_msg)
                # 可视化失败不应阻止整个流程，只记录错误
                logger.info("继续生成报告，跳过可视化...")

        # 生成最终报告
        logger.info("生成最终报告...")
        all_place_ids = orchestration.get_all_place_ids()
        points_processed = len(orchestration.cells)
        grid_points = [
            (cell.center_lat, cell.center_lng) for cell in orchestration.cells.values()
        ]

        # 计算实际触发细化的单元格数量
        refinements_triggered = sum(
            1
            for cell in orchestration.cells.values()
            if cell.status == SearchStatus.REFINEMENT_NEEDED
            or cell.status == SearchStatus.REFINEMENT_COMPLETE
        )

        generate_final_report(
            start_time=start_time,
            points_processed=points_processed,
            grid_points=grid_points,
            all_place_ids=all_place_ids,
            refinements_triggered=refinements_triggered,
            search_config=search_config,
            api_state=api_state,
            file_paths=file_paths,
            mode_slug="dry_run" if search_config.dry_run else "live",
        )

        logger.info("最终报告生成完成")

    except Exception as e:
        error_msg = f"生成可视化和报告失败: {e}"
        logger.error(error_msg)
        raise GridRuntimeError(error_msg) from e


def main():
    """主程序入口点"""
    try:
        # 解析命令行参数
        parser = create_parser()
        args = parser.parse_args()

        # 初始化日志系统
        log_level = args.log_level if hasattr(args, "log_level") else "INFO"
        log_file = None

        # 如果指定了工作目录，设置日志文件路径
        if hasattr(args, "work_dir") and args.work_dir:
            log_file = os.path.join(args.work_dir, "grid_search.log")

        configure_logging(log_level=log_level, log_file=log_file)

        logger.info("=== Google Maps Grid Search 启动 ===")
        logger.info(f"工作目录: {args.work_dir if hasattr(args, 'work_dir') else '.'}")

        # 1. 创建配置
        logger.info("创建配置...")
        config = create_application_config(args)
        logger.info("配置创建完成")

        # 2. 初始化应用程序状态
        logger.info("初始化应用程序状态...")
        app_state = ApplicationState(config)
        app_state.log_summary()

        # 3. 创建API客户端
        logger.info("创建API客户端...")
        api_client = APIClient(
            config=config,
            api_state=app_state.get_api_state(),
            search_config=app_state.get_search_config(),
            file_paths=app_state.get_file_paths(),
        )
        logger.info("API客户端创建完成")

        # 4. 设置搜索区域
        logger.info("设置搜索区域...")
        bounds = _setup_search_area(app_state, api_client)
        if bounds is None:
            raise RuntimeError("无法设置搜索区域")

        # 5. 创建状态管理器
        logger.info("创建状态管理器...")
        state_manager = create_state_manager(
            work_dir=app_state.get_file_paths().get_progress_dir(),
            state_file="orchestration.json",
        )
        logger.info("状态管理器创建完成")

        # 6. 创建或加载搜索编排对象
        logger.info("创建搜索编排对象...")
        orchestration = _create_or_load_orchestration(
            state_manager, app_state.get_search_config(), bounds, app_state
        )
        logger.info("搜索编排对象创建完成")

        # 7. 创建网格处理引擎和可视化服务
        logger.info("创建网格处理引擎...")

        # 使用工厂创建可视化服务
        visualization_callback = None

        # 从配置判断是否启用可视化
        use_visualization = getattr(app_state.get_search_config(), "visualize", False)

        if use_visualization:
            try:
                # 使用工厂创建可视化服务
                from src.visualization_factory import VisualizationFactory
                from src.visualization_event_handler import VisualizationEventHandler

                # 创建可视化服务
                progress_dir = str(app_state.get_file_paths().get_progress_dir())
                visualization_service = VisualizationFactory.create_service(
                    progress_dir
                )

                if visualization_service:
                    # 创建事件处理器
                    event_handler = VisualizationEventHandler(visualization_service)

                    # 设置可视化回调
                    visualization_callback = event_handler.handle_callback

                    logger.info("可视化服务和事件处理器初始化完成")
                else:
                    logger.info("可视化服务创建失败，将跳过可视化功能")

            except Exception as e:
                logger.warning(f"可视化服务初始化失败: {e}")

        grid_engine = _create_grid_engine(
            orchestration,
            api_client,
            state_manager,
            app_state.get_search_config(),
            visualization_callback=visualization_callback,
        )
        logger.info("网格处理引擎创建完成")

        # 8. 执行主搜索循环
        logger.info("开始主搜索循环...")
        start_time, total_api_calls = _run_search_loop(
            grid_engine, app_state.get_search_config(), app_state.get_api_state()
        )

        # 9. 生成可视化和报告
        logger.info("生成可视化和报告...")
        _generate_visualization_and_report(
            app_state,
            orchestration,
            app_state.get_api_state(),
            start_time,
            total_api_calls,
        )

        logger.info("=== 搜索完成! ===")
        app_state.log_summary()

    except KeyboardInterrupt:
        logger.info("用户中断操作")
        logger.info("程序已终止")
    except Exception as main_error:
        error_msg = f"严重错误: {main_error}"
        logger.error(f"*** {error_msg} ***")
        logger.error("脚本执行失败。需要人工介入检查错误并恢复。")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise GridRuntimeError(error_msg) from main_error


if __name__ == "__main__":
    main()
