import time
import os
import csv
import glob
import json
from typing import Optional
from .data_models import SearchOrchestration
from .logging_config import get_logger


# 获取日志记录器
logger = get_logger(__name__)


class ReportPaths:
    """报告文件路径配置类"""

    def __init__(self, work_dir: str):
        self.work_dir = work_dir
        self.output_file = os.path.join(work_dir, "output.json")
        self.summary_csv = os.path.join(work_dir, "summary.csv")
        self.detailed_data_dir = os.path.join(work_dir, "detailed_place_data")
        self.map_file = os.path.join(work_dir, "map.html")

        # 确保目录存在
        os.makedirs(self.detailed_data_dir, exist_ok=True)


def create_summary_csv(
    csv_filename: str, output_dir: str = "detailed_place_data"
) -> Optional[str]:
    """创建所有收集的地点数据的综合CSV摘要。"""
    # csv_filename 参数已经包含了完整的文件路径和文件名

    # 定义CSV标题
    headers = [
        "place_id",
        "name",
        "lat",
        "lng",
        "business_status",
        "rating",
        "user_ratings_total",
        "vicinity",
        "types",
    ]

    try:
        # 获取目录中的所有JSON文件
        json_files = glob.glob(os.path.join(output_dir, "*.json"))

        if not json_files:
            logger.warning("未找到详细地点数据进行汇总。")
            return None

        # 确保输出目录存在
        os.makedirs(os.path.dirname(csv_filename), exist_ok=True)

        # 写入CSV
        with open(csv_filename, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()

            for json_file in json_files:
                try:
                    with open(json_file, "r", encoding="utf-8") as f:
                        place_data = json.load(f)

                    # 为CSV转换数据
                    csv_row = {
                        "place_id": place_data.get("place_id", ""),
                        "name": place_data.get("name", ""),
                        "lat": place_data.get("location", {}).get("lat", ""),
                        "lng": place_data.get("location", {}).get("lng", ""),
                        "business_status": place_data.get("business_status", ""),
                        "rating": place_data.get("rating", ""),
                        "user_ratings_total": place_data.get("user_ratings_total", ""),
                        "vicinity": place_data.get("vicinity", ""),
                        "types": "|".join(place_data.get("types", [])),
                    }

                    writer.writerow(csv_row)
                except Exception as e:
                    logger.error(f"处理 {json_file} 时出错: {e}")

        logger.info(f"已创建CSV摘要: {csv_filename}")
        return csv_filename
    except Exception as e:
        logger.error(f"创建CSV摘要时出错: {e}")
        return None


# Removed deprecated handle_map_combining function


def generate_final_report(
    start_time,
    points_processed,
    grid_points,
    all_place_ids,
    refinements_triggered,
    search_config,
    api_state,
    file_paths,
    mode_slug,
):
    """生成最终报告和CSV摘要。"""
    # 最终报告
    elapsed = time.time() - start_time
    logger.info("\n\n--- 提取完成 ---")
    logger.info("最终统计信息:")
    logger.info(f"  - 运行时间: {elapsed:.1f} 秒")
    logger.info(f"  - 已处理点数: {points_processed}/{len(grid_points)}")
    logger.info(f"  - API调用次数: {api_state.api_call_count}")
    logger.info(f"  - 唯一地点ID数: {len(all_place_ids)}")
    logger.info(f"  - 触发的细化次数: {refinements_triggered}")
    logger.info(f"\n结果已保存到 {file_paths.output_file}")

    # Create CSV summary of all places
    output_dir_path = (
        os.path.dirname(file_paths.output_file)
        if os.path.dirname(file_paths.output_file)
        else "."
    )

    target_location_name = search_config.location
    if search_config.test_area and search_config.test_area in search_config.test_areas:
        target_location_name = search_config.test_areas[search_config.test_area]["name"]

    csv_file = create_summary_csv(
        csv_filename=file_paths.summary_csv, output_dir=file_paths.detailed_data_dir
    )
    if csv_file:
        logger.info(f"CSV摘要已保存到 {csv_file}")


def generate_report_from_orchestration(
    orchestration: SearchOrchestration, work_dir: str
):
    """直接从SearchOrchestration对象生成最终报告。

    Args:
        orchestration: SearchOrchestration对象
        work_dir: 工作目录路径
    """
    # 创建报告路径配置
    report_paths = ReportPaths(work_dir)

    # 从编排对象提取数据
    all_place_ids = orchestration.get_all_place_ids()
    summary = orchestration.get_summary()

    # 计算运行时间
    start_time = orchestration.created_at.timestamp()
    elapsed = time.time() - start_time

    # 保存编排对象到JSON文件
    orchestration.save_to_file(report_paths.output_file)

    # 生成最终报告
    logger.info("\n\n--- 提取完成 ---")
    logger.info("最终统计信息:")
    logger.info(f"  - 运行时间: {elapsed:.1f} 秒")
    logger.info(f"  - 已处理点数: {summary['total_cells']}")
    logger.info(f"  - API调用次数: {summary['metrics']['total_api_calls']}")
    logger.info(f"  - 唯一地点ID数: {len(all_place_ids)}")
    logger.info(
        f"  - 触发的细化次数: {len(orchestration.get_cells_needing_refinement())}"
    )
    logger.info(f"  - 完成层级: {summary['completed_layers']}")
    logger.info(f"  - 失败单元数: {summary['metrics']['failed_cells']}")
    logger.info(f"\n结果已保存到 {report_paths.output_file}")

    # 生成CSV摘要
    try:
        create_summary_csv(report_paths.summary_csv, report_paths.detailed_data_dir)
        logger.info(f"CSV摘要已保存到 {report_paths.summary_csv}")
    except Exception as e:
        logger.warning(f"生成CSV摘要时出错: {e}")

    # 生成可视化地图
    try:
        if orchestration.cells:
            generate_visualization_from_orchestration(
                orchestration=orchestration,
                output_file=report_paths.map_file,
                is_final=True,
            )
            logger.info(f"地图已保存到 {report_paths.map_file}")
    except Exception as e:
        logger.warning(f"生成地图时出错: {e}")

    return report_paths.output_file
