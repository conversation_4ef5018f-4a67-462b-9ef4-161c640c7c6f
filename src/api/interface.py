"""
API interface definitions for Google Places API.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional


class PlacesAPIInterface(ABC):
    """Abstract base class for Places API implementations."""

    @abstractmethod
    def perform_nearby_search(
        self,
        lat: float,
        lng: float,
        radius: float,
        place_type: str,
        next_page_token: Optional[str] = None,
        refine_level: int = 0,
    ) -> Dict[str, Any]:
        """Perform a nearby search using the Google Places API."""
        pass

    @abstractmethod
    def search_places(
        self,
        query: str,
        location: Optional[str] = None,
        radius: Optional[int] = None,
        max_results: Optional[int] = None,
        region: Optional[str] = None,
        language: Optional[str] = None,
        min_rating: Optional[float] = None,
        price_level: Optional[int] = None,
        open_now: Optional[bool] = None,
        place_type: Optional[str] = None,
        fields: Optional[List[str]] = None,
    ) -> List[Dict[str, Any]]:
        """Search for places using the Google Places API."""
        pass

    @abstractmethod
    def get_place_details(
        self, place_id: str, fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Get detailed information about a place using its place ID."""
        pass

    @abstractmethod
    def get_place_photos(
        self,
        place_id: str,
        max_width: Optional[int] = None,
        max_height: Optional[int] = None,
    ) -> List[str]:
        """Get photos for a place using its place ID."""
        pass

    @abstractmethod
    def geocoding_search(self, location: str) -> Dict[str, Any]:
        """Perform a geocoding search to get coordinates for a location."""
        pass

    @abstractmethod
    def get_bounding_box(self, location: str) -> Optional[tuple]:
        """Get bounding box for a location.

        Args:
            location: Location name/address to geocode

        Returns:
            Tuple of (min_lat, min_lng, max_lat, max_lng) or None if failed
        """
        pass
