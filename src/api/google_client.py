"""
Google Places API client implementation.
"""

import os
import time
import requests
from typing import List, Dict, Any, Optional
from .interface import PlacesAPIInterface
from ..logging_config import get_logger

# Get logger
logger = get_logger(__name__)


class GooglePlacesAPI(PlacesAPIInterface):
    """Google Places API client."""

    def __init__(self, api_key: Optional[str] = None):
        """Initialize the Google Places API client."""
        self.api_key = api_key or os.getenv("GOOGLE_MAPS_API_KEY")
        if not self.api_key:
            raise ValueError("Google Places API key is required")
        self.base_url = "https://maps.googleapis.com/maps/api/place"
        self.session = requests.Session()
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 100ms between requests to avoid rate limiting

    def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Make a request to the Google Places API with rate limiting."""
        # Ensure we don't make requests too quickly
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        if time_since_last_request < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last_request)

        params["key"] = self.api_key
        url = f"{self.base_url}/{endpoint}/json"
        response = self.session.get(url, params=params)
        response.raise_for_status()
        self.last_request_time = time.time()

        data = response.json()
        return data

    def perform_nearby_search(
        self,
        lat: float,
        lng: float,
        radius: float,
        place_type: str,
        next_page_token: Optional[str] = None,
        refine_level: int = 0,
    ) -> Dict[str, Any]:
        """Perform a nearby search using the Google Places API."""
        if next_page_token:
            # Use page token for pagination
            params = {
                "pagetoken": next_page_token,
            }
        else:
            # Initial search
            params = {
                "location": f"{lat},{lng}",
                "radius": radius,
                "type": place_type,
            }

        data = self._make_request("nearbysearch", params)
        return data

    def search_places(
        self,
        query: str,
        location: Optional[str] = None,
        radius: Optional[int] = None,
        max_results: Optional[int] = None,
        region: Optional[str] = None,
        language: Optional[str] = None,
        min_rating: Optional[float] = None,
        price_level: Optional[int] = None,
        open_now: Optional[bool] = None,
        place_type: Optional[str] = None,
        fields: Optional[List[str]] = None,
    ) -> List[Dict[str, Any]]:
        """Search for places using the Google Places API."""
        params = {
            "query": query,
        }

        if location:
            params["location"] = location
        if radius:
            params["radius"] = radius
        if region:
            params["region"] = region
        if language:
            params["language"] = language
        if min_rating is not None:
            params["minprice"] = min_rating
        if price_level is not None:
            params["maxprice"] = price_level
        if open_now is not None:
            params["opennow"] = open_now
        if place_type:
            params["type"] = place_type

        data = self._make_request("textsearch", params)
        results = data.get("results", [])

        # Apply additional filters that can't be done in the API request
        if min_rating is not None:
            results = [r for r in results if r.get("rating", 0) >= min_rating]

        # Limit results if requested
        if max_results is not None and len(results) > max_results:
            results = results[:max_results]

        return results

    def get_place_details(
        self, place_id: str, fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Get detailed information about a place using its place ID."""
        params = {
            "place_id": place_id,
        }

        if fields:
            params["fields"] = ",".join(fields)

        data = self._make_request("details", params)
        return data.get("result", {})

    def get_place_photos(
        self,
        place_id: str,
        max_width: Optional[int] = None,
        max_height: Optional[int] = None,
    ) -> List[str]:
        """Get photos for a place using its place ID."""
        params = {
            "place_id": place_id,
        }

        if max_width:
            params["maxwidth"] = max_width
        if max_height:
            params["maxheight"] = max_height

        data = self._make_request("photos", params)
        photos = data.get("result", {}).get("photos", [])

        # Convert photo references to URLs
        photo_urls = []
        for photo in photos:
            photo_reference = photo.get("photo_reference")
            if photo_reference:
                photo_url = f"https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&photoreference={photo_reference}&key={self.api_key}"
                photo_urls.append(photo_url)

        return photo_urls

    def geocoding_search(self, location: str) -> Dict[str, Any]:
        """Perform a geocoding search to get coordinates for a location."""
        geocoding_url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {"address": location, "key": self.api_key}

        response = self.session.get(geocoding_url, params=params)
        response.raise_for_status()

        data = response.json()
        return data

    def get_bounding_box(self, location: str) -> Optional[tuple]:
        """Get bounding box for a location using Google Maps Geocoding API.

        Args:
            location: Location name/address to geocode

        Returns:
            Tuple of (min_lat, min_lng, max_lat, max_lng) or None if failed
        """
        logger.info(f"Getting bounding box for {location}...")

        data = self.geocoding_search(location)

        if data["status"] != "OK":
            logger.error(f"Error: {data['status']}")
            return None

        # Get viewport (bounding box)
        viewport = data["results"][0]["geometry"]["viewport"]

        # Extract coordinates
        northeast = viewport["northeast"]
        southwest = viewport["southwest"]

        # Return as (min_lat, min_lng, max_lat, max_lng)
        return (southwest["lat"], southwest["lng"], northeast["lat"], northeast["lng"])
