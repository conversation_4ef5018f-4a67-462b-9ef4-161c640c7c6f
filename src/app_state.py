"""
应用状态管理模块

为 Google Maps Grid Search 项目提供运行时状态管理。
配置管理已完全移至 config.py，本模块只负责状态管理。
"""

import os
from typing import Optional, Set, List
from dataclasses import dataclass, field

from src.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class ApiState:
    """API状态管理"""

    api_call_count: int = 0
    current_delay: float = 1.0
    consecutive_errors: int = 0

    def increment_call_count(self) -> None:
        """增加API调用计数"""
        self.api_call_count += 1
        logger.debug(f"API调用次数: {self.api_call_count}")

    def handle_rate_limit(self, base_delay: float, max_delay: float) -> None:
        """处理速率限制"""
        self.consecutive_errors += 1
        self.current_delay = min(base_delay * (2**self.consecutive_errors), max_delay)
        logger.warning(f"达到速率限制，延迟增加到 {self.current_delay} 秒")

    def reset_error_state(self) -> None:
        """重置错误状态"""
        self.consecutive_errors = 0
        self.current_delay = 1.0
        logger.debug("API错误状态已重置")


@dataclass
class SearchState:
    """搜索状态管理"""

    all_place_ids: Set[str] = field(default_factory=set)
    completed_points: Set[str] = field(default_factory=set)
    refining_points: Set[str] = field(default_factory=set)
    searched_mini_areas: Set[str] = field(default_factory=set)
    processed_grid_points: List[tuple] = field(default_factory=list)
    all_refinement_points: List[tuple] = field(default_factory=list)
    search_bounds: Optional[tuple] = None

    def add_place_ids(self, place_ids: Set[str]) -> None:
        """添加地点ID"""
        self.all_place_ids.update(place_ids)
        logger.debug(
            f"新增 {len(place_ids)} 个地点ID，总计 {len(self.all_place_ids)} 个"
        )

    def add_completed_point(self, point: tuple) -> None:
        """添加已完成点"""
        self.completed_points.add(f"{point[0]}_{point[1]}")

    def add_refining_point(self, point: tuple) -> None:
        """添加细化点"""
        self.refining_points.add(f"{point[0]}_{point[1]}")

    def add_searched_mini_area(self, area_key: str) -> None:
        """添加已搜索的小区域"""
        self.searched_mini_areas.add(area_key)

    def set_search_bounds(self, bounds: tuple) -> None:
        """设置搜索边界"""
        self.search_bounds = bounds
        logger.info(f"搜索边界已设置: {bounds}")


@dataclass
class VisualizationState:
    """可视化状态管理"""

    place_coords_for_viz: List[tuple] = field(default_factory=list)
    last_visualization_file: Optional[str] = None
    _viz_place_ids_set: Set[str] = field(default_factory=set)

    def add_place_coordinates(self, lat: float, lng: float, place_id: str) -> None:
        """添加地点坐标"""
        # 避免重复添加相同地点
        if place_id not in self._viz_place_ids_set:
            self.place_coords_for_viz.append((lat, lng))
            self._viz_place_ids_set.add(place_id)

    def get_place_count(self) -> int:
        """获取可视化地点数量"""
        return len(self.place_coords_for_viz)

    def clear(self) -> None:
        """清空可视化状态"""
        self.place_coords_for_viz.clear()
        self._viz_place_ids_set.clear()
        self.last_visualization_file = None
        logger.debug("可视化状态已清空")


class FilePaths:
    """文件路径管理"""

    def __init__(self, work_dir: str, visualize: bool):
        self.output_base_dir = "output"

        # 处理当前目录情况，避免路径中出现 "./"
        if work_dir == "." or work_dir == "":
            self.work_output_dir = os.path.join(self.output_base_dir, "default")
        else:
            self.work_output_dir = os.path.join(self.output_base_dir, work_dir)

        # 设置文件路径
        self.output_file = os.path.join(self.work_output_dir, "found_place_ids.json")
        self.progress_file = os.path.join(self.work_output_dir, "orchestration.json")
        self.near_limit_log = os.path.join(
            self.work_output_dir, "near_limit_points.csv"
        )
        self.refinement_log = os.path.join(self.work_output_dir, "refinement_log.csv")
        self.detailed_data_dir = os.path.join(
            self.work_output_dir, "detailed_place_data"
        )
        self.summary_csv = os.path.join(self.work_output_dir, "summary.csv")
        self.real_time_visualization_file = (
            os.path.join(self.work_output_dir, "visualization_map.html")
            if visualize
            else None
        )
        self.map_data_file = os.path.join(self.work_output_dir, "map_data.json")

        # 确保目录存在
        self._ensure_directories_exist()

    def _ensure_directories_exist(self) -> None:
        """确保目录存在"""
        os.makedirs(self.work_output_dir, exist_ok=True)
        os.makedirs(self.detailed_data_dir, exist_ok=True)
        logger.debug(f"确保目录存在: {self.work_output_dir}")

    def get_output_dir(self) -> str:
        """获取输出目录路径"""
        return self.work_output_dir

    def get_progress_dir(self) -> str:
        """获取进度文件目录"""
        return os.path.dirname(self.progress_file)


class ApplicationState:
    """应用程序状态管理器
    
    接收统一的配置对象，负责运行时状态管理。
    所有配置处理已在 config.py 中完成。
    """

    def __init__(self, config):
        """初始化应用程序状态"""
        from src.config import ApplicationConfig
        
        logger.info("初始化应用程序状态...")

        # 确保接收的是配置对象
        if not isinstance(config, ApplicationConfig):
            raise TypeError("config 必须是 ApplicationConfig 类型")

        # 保存配置对象（替代原来的 search_config）
        self.config = config
        self.search_config = config  # 保持兼容性

        # 创建文件路径对象
        self.file_paths = FilePaths(config.work_dir, config.visualize)

        # 创建API状态对象
        self.api_state = ApiState()

        # 创建搜索状态对象
        self.search_state = SearchState()

        # 创建可视化状态对象
        self.visualization_state = VisualizationState()

        # 设置mock数据随机种子
        if config.dry_run:
            os.environ["MOCK_SEED"] = str(config.mock_seed)
            logger.info(f"模拟模式已启用，随机种子: {config.mock_seed}")

        logger.info("应用程序状态初始化完成")

    def get_api_state(self) -> ApiState:
        """获取API状态"""
        return self.api_state

    def get_search_config(self):
        """获取搜索配置（实际上是完整的应用配置）"""
        return self.config

    def get_search_state(self) -> SearchState:
        """获取搜索状态"""
        return self.search_state

    def get_visualization_state(self) -> VisualizationState:
        """获取可视化状态"""
        return self.visualization_state

    def get_file_paths(self) -> FilePaths:
        """获取文件路径"""
        return self.file_paths

    def log_summary(self) -> None:
        """记录状态摘要"""
        logger.info("=== 应用程序状态摘要 ===")
        logger.info(f"工作目录: {self.config.work_dir}")
        logger.info(f"搜索地点类型: {self.config.place_type}")

        # 显示实际使用的位置信息
        if (self.config.test_area and
            self.config.test_area != "all" and
            self.config.test_area in self.config.test_areas):
            area_info = self.config.test_areas[self.config.test_area]
            logger.info(f"目标位置: {area_info['name']} (测试区域: {self.config.test_area})")
        else:
            logger.info(f"目标位置: {self.config.location}")

        logger.info(f"试运行模式: {self.config.dry_run}")
        logger.info(f"可视化模式: {self.config.visualize}")
        logger.info(f"最大API调用次数: {self.config.max_calls}")
        logger.info(f"已发现地点数量: {len(self.search_state.all_place_ids)}")
        logger.info(f"API调用次数: {self.api_state.api_call_count}")
        logger.info("=========================")
