"""
可视化工厂模块

为 Google Maps Grid Search 项目提供可视化服务的工厂创建功能。
实现工厂模式，降低模块间耦合度，提升可测试性和可维护性。

主要功能：
- 统一的可视化服务创建接口
- 错误处理和日志记录
- 可用性检查和状态报告
- 配置验证和参数校验

使用示例：
    >>> factory = VisualizationFactory()
    >>> service = factory.create_service("./output")
    >>> if service:
    ...     service.update_on_initialization(orchestration)
"""

import os
from typing import Optional, Dict, Any
from pathlib import Path
import importlib.util

from src.logging_config import get_logger

logger = get_logger(__name__)


class VisualizationFactory:
    """可视化服务工厂类

    负责创建和配置可视化服务，提供统一的创建接口。
    实现工厂模式，降低主程序对具体可视化实现的依赖。

    这个类提供了完整的可视化服务生命周期管理，包括：
    - 服务创建和配置验证
    - 依赖检查和可用性验证
    - 错误处理和恢复机制
    - 状态监控和诊断信息

    设计原则：
    - 单一职责：专门负责可视化服务的创建
    - 开闭原则：易于扩展新的可视化服务类型
    - 依赖倒置：依赖于抽象而非具体实现
    - 错误隔离：创建失败不影响主程序流程

    Attributes:
        logger: 日志记录器实例
    """

    @staticmethod
    def create_service(
        work_dir: str, config: Optional[Dict[str, Any]] = None
    ) -> Optional["VisualizationService"]:
        """创建可视化服务的工厂方法

        根据工作目录和配置创建可视化服务实例。这是工厂的核心方法，
        提供了完整的错误处理和验证机制。

        创建过程包括以下步骤：
        1. 输入参数验证（工作目录、配置）
        2. 依赖模块可用性检查
        3. 工作目录存在性和权限验证
        4. 服务实例化和初始化

        Args:
            work_dir: 工作目录路径，用于存储可视化文件。
                      必须是非空字符串，且程序有写入权限
            config: 可视化配置字典，如果为None则使用默认配置。
                   包含更新间隔、文件数量限制等参数

        Returns:
            Optional[VisualizationService]: 创建成功的可视化服务实例，
                                         如果创建失败则返回None。
                                         返回None不会影响主程序流程。

        Raises:
            ValueError: 当工作目录路径无效（空值、非字符串、权限不足）
            ImportError: 当可视化服务模块不可用时（可选，内部捕获）

        Note:
            - 此方法实现了优雅的错误处理，创建失败不会抛出异常
            - 工作目录如果不存在会自动创建
            - 依赖模块缺失会跳过可视化功能但不会中断程序

        Example:
            >>> # 使用默认配置创建服务
            >>> service = VisualizationFactory.create_service("./output")
            >>> if service:
            ...     service.update_on_initialization(orchestration)

            >>> # 使用自定义配置创建服务
            >>> config = {"update_interval": 10.0, "max_file_count": 5}
            >>> service = VisualizationFactory.create_service("./output", config)
        """
        try:
            # 动态导入，避免在不需要时加载依赖
            from src.visualization_service import (
                VisualizationService,
                FOLIUM_AVAILABLE,
            )

            # 检查folium可用性
            if not FOLIUM_AVAILABLE:
                logger.warning("Folium库不可用，无法创建可视化服务")
                return None

            # 验证工作目录
            if not work_dir or not isinstance(work_dir, str):
                raise ValueError("工作目录必须是非空字符串")

            # 清理路径
            work_dir = work_dir.strip()

            # 确保目录存在
            Path(work_dir).mkdir(parents=True, exist_ok=True)

            # 创建服务实例
            service = VisualizationService(work_dir=work_dir, config=config)

            logger.info(f"可视化服务创建成功: {work_dir}")
            return service

        except ImportError as e:
            logger.warning(f"可视化服务模块不可用，跳过可视化功能: {e}")
            return None
        except ValueError as e:
            logger.error(f"可视化服务创建失败 - 参数错误: {e}")
            return None
        except Exception as e:
            logger.error(f"可视化服务创建失败 - 未知错误: {e}")
            return None

    @staticmethod
    def is_visualization_available() -> bool:
        """检查可视化功能是否可用

        通过检查关键依赖模块的规格来验证可视化功能的可用性。
        这是轻量级检查，不会创建实际的服务实例。

        检查内容：
        - visualization_service 模块是否可导入
        - folium 库是否已安装
        - 基本的依赖是否满足

        Returns:
            bool: 可视化功能是否可用。True表示所有依赖都可用，
                  False表示至少有一个依赖不可用。

        Note:
            - 此方法只检查依赖可用性，不验证配置或权限
            - 返回False并不意味着程序无法运行，只是可视化功能被禁用
            - 可以在程序启动时调用此方法来决定是否启用可视化功能

        Example:
            >>> if VisualizationFactory.is_visualization_available():
            ...     print("可视化功能可用")
            ... else:
            ...     print("可视化功能不可用，将跳过相关功能")
        """
        # 检查folium库是否可用
        folium_spec = importlib.util.find_spec("folium")
        if not folium_spec:
            return False

        # 检查visualization_service模块是否可用
        vis_service_spec = importlib.util.find_spec("src.visualization_service")
        if not vis_service_spec:
            return False

        return True

    @staticmethod
    def get_visualization_status() -> Dict[str, Any]:
        """获取可视化功能状态信息

        提供详细的可视化系统状态诊断信息，用于调试和监控。
        此方法执行全面的检查，包括依赖、权限、配置等方面。

        检查项目：
        - available: 整体可视化功能是否可用
        - folium_available: folium库是否可用及版本信息
        - service_available: 可视化服务模块是否可用
        - work_dir_check: 工作目录权限状态检查

        Returns:
            Dict[str, Any]: 包含详细状态信息的字典，结构如下：
                {
                    "available": bool,
                    "folium_available": bool,
                    "folium_version": Optional[str],
                    "service_available": bool,
                    "work_dir_check": Optional[str]
                }

        Note:
            - 此方法提供比 is_visualization_available() 更详细的信息
            - 可以用于诊断可视化功能问题的原因
            - 返回的信息适合用于日志记录和调试输出

        Example:
            >>> status = VisualizationFactory.get_visualization_status()
            >>> print(f"可视化状态: {status}")
            >>> if not status["available"]:
            ...     print(f"问题原因: folium可用={status['folium_available']}")
            ...     print(f"服务可用: {status['service_available']}")
        """
        status = {
            "available": VisualizationFactory.is_visualization_available(),
            "folium_available": False,
            "service_available": False,
            "work_dir_check": None,
        }

        try:
            import folium

            status["folium_available"] = True
            status["folium_version"] = getattr(folium, "__version__", "unknown")
        except ImportError:
            pass

        # 检查visualization_service模块是否可用
        vis_service_spec = importlib.util.find_spec("src.visualization_service")
        if vis_service_spec:
            status["service_available"] = True

        # 检查工作目录
        try:
            work_dir = os.getcwd()
            if os.access(work_dir, os.W_OK):
                status["work_dir_check"] = "writable"
            else:
                status["work_dir_check"] = "not_writable"
        except Exception:
            status["work_dir_check"] = "error"

        return status
