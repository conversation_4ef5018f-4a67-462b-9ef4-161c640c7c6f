"""
可视化异常处理模块

为 Google Maps Grid Search 项目提供统一的可视化异常处理机制。
定义专门的异常类型和错误处理装饰器，标准化错误处理流程。
"""

import logging

# 创建模块级别的logger
logger = logging.getLogger(__name__)


class VisualizationError(Exception):
    """可视化基础异常类

    所有可视化相关异常的基类，提供统一的异常处理接口。
    """

    def __init__(
        self, message: str, error_code: str = None, original_error: Exception = None
    ):
        """
        初始化可视化异常

        Args:
            message: 错误消息
            error_code: 错误代码，用于错误分类和处理
            original_error: 原始异常，用于错误追踪
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "VISUALIZATION_ERROR"
        self.original_error = original_error

    def __str__(self) -> str:
        """返回异常的字符串表示"""
        if self.original_error:
            return (
                f"[{self.error_code}] {self.message} (原始错误: {self.original_error})"
            )
        return f"[{self.error_code}] {self.message}"


class VisualizationInitializationError(VisualizationError):
    """可视化初始化异常

    当可视化服务初始化过程中出现错误时抛出。
    """

    def __init__(
        self, message: str, service_type: str = None, original_error: Exception = None
    ):
        """
        初始化可视化初始化异常

        Args:
            message: 错误消息
            service_type: 服务类型（如 'folium', 'service'）
            original_error: 原始异常
        """
        error_code = f"VISUALIZATION_INIT_ERROR"
        if service_type:
            error_code = f"VISUALIZATION_{service_type.upper()}_INIT_ERROR"

        super().__init__(message, error_code, original_error)
        self.service_type = service_type


class VisualizationUpdateError(VisualizationError):
    """可视化更新异常

    当可视化更新过程中出现错误时抛出。
    """

    def __init__(
        self, message: str, update_type: str = None, original_error: Exception = None
    ):
        """
        初始化可视化更新异常

        Args:
            message: 错误消息
            update_type: 更新类型（如 'cell_processed', 'refinement', 'initialization'）
            original_error: 原始异常
        """
        error_code = "VISUALIZATION_UPDATE_ERROR"
        if update_type:
            error_code = f"VISUALIZATION_{update_type.upper()}_UPDATE_ERROR"

        super().__init__(message, error_code, original_error)
        self.update_type = update_type


class VisualizationConfigurationError(VisualizationError):
    """可视化配置异常

    当可视化配置出现错误时抛出。
    """

    def __init__(
        self, message: str, config_key: str = None, original_error: Exception = None
    ):
        """
        初始化可视化配置异常

        Args:
            message: 错误消息
            config_key: 配置键名
            original_error: 原始异常
        """
        error_code = "VISUALIZATION_CONFIG_ERROR"
        if config_key:
            error_code = f"VISUALIZATION_{config_key.upper()}_CONFIG_ERROR"

        super().__init__(message, error_code, original_error)
        self.config_key = config_key


class VisualizationFileError(VisualizationError):
    """可视化文件操作异常

    当可视化文件操作出现错误时抛出。
    """

    def __init__(
        self,
        message: str,
        file_path: str = None,
        operation: str = None,
        original_error: Exception = None,
    ):
        """
        初始化可视化文件异常

        Args:
            message: 错误消息
            file_path: 文件路径
            operation: 操作类型（如 'read', 'write', 'create'）
            original_error: 原始异常
        """
        error_code = "VISUALIZATION_FILE_ERROR"
        if operation:
            error_code = f"VISUALIZATION_{operation.upper()}_FILE_ERROR"

        super().__init__(message, error_code, original_error)
        self.file_path = file_path
        self.operation = operation


def handle_visualization_errors(func):
    """
    统一的错误处理装饰器

    为可视化相关函数提供统一的错误处理机制，包括：
    - 异常捕获和转换
    - 日志记录
    - 错误恢复

    Args:
        func: 要装饰的函数

    Returns:
        装饰后的函数

    Example:
        >>> @handle_visualization_errors
        ... def update_visualization(orchestration):
        ...     # 可视化更新逻辑
        ...     pass
    """
    import functools

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except VisualizationError:
            # 已知的可视化错误，记录为warning级别
            logger.warning(
                f"可视化操作失败: {func.__name__} - {args[0] if args else ''}"
            )
            return None
        except ImportError as e:
            # 模块导入错误，记录为info级别
            logger.info(f"可视化模块不可用，跳过操作: {func.__name__} - {e}")
            return None
        except PermissionError as e:
            # 权限错误，记录为warning级别
            logger.warning(f"可视化操作权限不足: {func.__name__} - {e}")
            return None
        except FileNotFoundError as e:
            # 文件不存在错误，记录为info级别
            logger.info(f"可视化文件不存在: {func.__name__} - {e}")
            return None
        except Exception as e:
            # 未知错误，记录为error级别
            logger.error(f"可视化操作异常: {func.__name__} - {e}")
            # 可以选择重新抛出特定类型的异常
            if isinstance(e, (ValueError, TypeError)):
                raise VisualizationConfigurationError(
                    f"参数错误: {e}", original_error=e
                )
            else:
                raise VisualizationError(f"未知错误: {e}", original_error=e)

    return wrapper


def safe_visualization_operation(default_return=None, log_level="warning"):
    """
    安全的可视化操作装饰器

    提供更灵活的错误处理选项，允许指定默认返回值和日志级别。

    Args:
        default_return: 出错时的默认返回值
        log_level: 日志级别（"debug", "info", "warning", "error"）

    Returns:
        装饰器函数

    Example:
        >>> @safe_visualization_operation(default_return=False, log_level="debug")
        ... def check_visualization_available():
        ...     # 检查可视化可用性
        ...     pass
    """
    import functools

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                log_method = getattr(logger, log_level.lower(), logger.warning)
                log_method(f"可视化操作安全失败: {func.__name__} - {e}")
                return default_return

        return wrapper

    return decorator


def create_visualization_error_context(operation: str, **context):
    """
    创建可视化错误上下文

    为错误报告提供额外的上下文信息。

    Args:
        operation: 操作名称
        **context: 额外的上下文信息

    Returns:
        dict: 错误上下文字典

    Example:
        >>> context = create_visualization_error_context(
        ...     operation="update_map",
        ...     file_path="/path/to/map.html",
        ...     cell_count=42
        ... )
    """
    import time
    from datetime import datetime

    context_dict = {
        "operation": operation,
        "timestamp": datetime.now().isoformat(),
        "unix_timestamp": time.time(),
    }
    context_dict.update(context)

    return context_dict


def log_visualization_error(error: Exception, context: dict = None):
    """
    记录可视化错误

    提供统一的错误记录格式。

    Args:
        error: 异常对象
        context: 错误上下文字典
    """
    error_info = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "error_code": getattr(error, "error_code", "UNKNOWN_ERROR"),
        "context": context or {},
    }

    if isinstance(error, VisualizationError):
        logger.warning(f"可视化错误: {error_info}")
    else:
        logger.error(f"未知可视化异常: {error_info}")


# 异常恢复工具函数
def is_recoverable_error(error: Exception) -> bool:
    """
    判断错误是否可恢复

    Args:
        error: 异常对象

    Returns:
        bool: 错误是否可恢复

    可恢复的错误：
    - 文件不存在
    - 权限不足
    - 网络问题
    - 暂时性资源问题
    """
    unrecoverable_errors = (
        ValueError,
        TypeError,
        MemoryError,
        SyntaxError,
        IndentationError,
    )

    return not isinstance(error, unrecoverable_errors)


def get_error_recovery_suggestion(error: Exception) -> str:
    """
    获取错误恢复建议

    Args:
        error: 异常对象

    Returns:
        str: 恢复建议
    """
    if isinstance(error, ImportError):
        return "请检查所需的可视化库是否已正确安装"
    elif isinstance(error, PermissionError):
        return "请检查文件权限，确保程序有读写权限"
    elif isinstance(error, FileNotFoundError):
        return "请检查文件路径是否正确，或创建必要的目录"
    elif isinstance(error, VisualizationConfigurationError):
        return "请检查可视化配置参数是否正确"
    elif isinstance(error, VisualizationInitializationError):
        return "请检查可视化服务是否正确初始化"
    elif isinstance(error, VisualizationUpdateError):
        return "请检查可视化更新参数是否正确"
    elif isinstance(error, VisualizationFileError):
        return "请检查文件路径和操作权限"
    else:
        return "请检查系统状态和配置参数"
