"""
异常类型模块

为 Google Maps Grid Search 项目提供统一的异常类型体系。
这些异常类型支持精确的错误处理和人工介入机制。

基于 TECHNICAL_DESIGN.md 中的错误处理设计规范。
"""

from typing import Optional


class GridSearchError(Exception):
    """基础异常类

    所有项目特定异常的基类，提供统一的错误处理接口。
    """

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[dict] = None,
    ):
        """初始化异常

        Args:
            message: 错误消息
            error_code: 错误代码，用于程序化处理
            details: 错误详细信息字典
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}

    def __str__(self) -> str:
        """返回异常的字符串表示"""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class ConfigurationError(GridSearchError):
    """配置错误

    当配置文件、环境变量或命令行参数出现问题时抛出。
    这类错误通常需要人工介入修正配置后重新运行。
    """

    pass


class NetworkError(GridSearchError):
    """网络相关错误

    当网络连接、API请求失败时抛出。
    这类错误可能是暂时的，但通常需要人工介入检查网络状态或API服务。
    """

    pass


class APIError(GridSearchError):
    """API调用错误

    当Google Maps API返回错误响应时抛出。
    包括API限制、配额用完、认证失败等情况。
    """

    pass


class APIRateLimitError(APIError):
    """API限制错误

    当达到API调用限制时抛出。
    这类错误通常需要等待一段时间或增加配额后才能继续。
    """

    pass


class APIAuthenticationError(APIError):
    """API认证错误

    当API密钥无效、过期或权限不足时抛出。
    这类错误需要人工介入更新API密钥或权限。
    """

    pass


class DataError(GridSearchError):
    """数据处理错误

    当数据解析、序列化、验证失败时抛出。
    这类错误通常指示数据格式问题或数据损坏。
    """

    pass


class DataCorruptionError(DataError):
    """数据损坏错误

    当数据文件损坏或无法正确读取时抛出。
    这类错误通常需要人工介入检查数据完整性。
    """

    pass


class StateManagementError(GridSearchError):
    """状态管理错误

    当状态保存、加载、恢复失败时抛出。
    这类错误可能影响搜索的恢复能力，需要人工介入检查状态文件。
    """

    pass


class ProcessingError(GridSearchError):
    """处理错误

    当搜索处理过程中的算法错误或逻辑错误时抛出。
    这类错误通常指示程序逻辑问题，需要开发者介入。
    """

    pass


class CellProcessingError(ProcessingError):
    """网格单元处理错误

    当处理单个网格单元时发生错误时抛出。
    可能是特定区域的搜索问题或数据问题。
    """

    pass


class FileSystemError(GridSearchError):
    """文件系统错误

    当文件操作（创建、读取、写入、删除）失败时抛出。
    这类错误通常与权限、磁盘空间或文件路径有关。
    """

    pass


class ValidationError(GridSearchError):
    """验证错误

    当输入数据验证失败时抛出。
    包括参数验证、结果验证等各种验证场景。
    """

    pass


class GridRuntimeError(GridSearchError):
    """运行时错误

    当程序运行时的其他错误情况时抛出。
    这是通用错误类型的兜底分类。
    """

    pass


def should_terminate_program(exception: Exception) -> bool:
    """判断异常是否应该导致程序终止

    Args:
        exception: 异常对象

    Returns:
        bool: 如果异常严重到需要程序终止则返回True
    """
    # 需要人工介入的严重错误
    termination_errors = (
        ConfigurationError,
        APIAuthenticationError,
        DataCorruptionError,
        StateManagementError,
        FileSystemError,
    )

    return isinstance(exception, termination_errors)


def should_retry_operation(exception: Exception) -> bool:
    """判断异常是否应该触发重试

    Args:
        exception: 异常对象

    Returns:
        bool: 如果异常是暂时的且可以重试则返回True
    """
    # 可以重试的暂时性错误
    retry_errors = (NetworkError, APIRateLimitError)

    return isinstance(exception, retry_errors)


def get_user_action_message(exception: Exception) -> str:
    """获取针对异常的用户操作建议

    Args:
        exception: 异常对象

    Returns:
        str: 用户操作建议消息
    """
    if isinstance(exception, ConfigurationError):
        return "请检查配置文件和环境变量设置，然后重新运行程序。"

    elif isinstance(exception, APIAuthenticationError):
        return "请检查Google Maps API密钥的有效性和权限，必要时更新API密钥。"

    elif isinstance(exception, APIRateLimitError):
        return "已达到API调用限制。请等待一段时间后再试，或考虑升级API配额。"

    elif isinstance(exception, NetworkError):
        return "请检查网络连接状态，确认可以访问Google Maps API服务。"

    elif isinstance(exception, DataCorruptionError):
        return "数据文件可能已损坏。请检查数据文件完整性，或删除损坏文件重新运行。"

    elif isinstance(exception, StateManagementError):
        return "状态管理出现问题。请检查状态文件，或删除状态文件重新开始搜索。"

    elif isinstance(exception, FileSystemError):
        return "文件操作失败。请检查文件权限和磁盘空间，确保工作目录可写。"

    elif isinstance(exception, ValidationError):
        return "输入参数验证失败。请检查输入参数的有效性。"

    elif isinstance(exception, ProcessingError):
        return "处理过程中发生错误。这可能需要开发者介入调试。"

    else:
        return "发生未知错误。请检查日志文件了解详情，考虑寻求技术支持。"
