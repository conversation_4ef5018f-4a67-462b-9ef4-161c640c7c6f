"""
API客户端模块

为 Google Maps Grid Search 项目提供统一的API客户端接口。
根据配置选择真实或模拟API，提供统一的接口和严格的异常处理。

异常处理标准：遇到任何异常时，立即停止并需要人工介入。
"""

import time
import requests
from typing import Dict, Any, Optional
from src.api import PlacesAPIInterface, GooglePlacesAPI, MockPlacesAPI
from src.logging_config import get_logger
from src.exceptions import (
    NetworkError,
    DataError,
    APIError,
    APIRateLimitError,
    GridRuntimeError,
)

# 获取日志记录器
logger = get_logger(__name__)


class APIClient:
    """
    统一的API客户端。

    根据配置选择真实或模拟API，提供统一的接口和严格的异常处理。
    遵循"失败时停止并需要人工介入"的标准。
    """

    def __init__(
        self,
        config,
        api_state: object,
        search_config: object,
        file_paths: object,
    ):
        """初始化API客户端

        Args:
            config: 应用配置对象
            api_state: API状态对象
            search_config: 搜索配置对象
            file_paths: 文件路径对象

        Raises:
            ValueError: 配置参数无效时抛出
            RuntimeError: 初始化失败时抛出
        """
        try:
            self.config = config
            self.api_state = api_state
            self.search_config = search_config
            self.file_paths = file_paths

            # 验证必需的配置
            if not hasattr(config, "dry_run"):
                raise ValueError("config 必须包含 dry_run 属性")

            if not hasattr(config, "mock_seed"):
                raise ValueError("config 必须包含 mock_seed 属性")

            if not hasattr(config, "max_calls"):
                raise ValueError("config 必须包含 max_calls 属性")

            # 根据配置选择API实现
            if config.dry_run:
                logger.info("使用模拟API客户端")
                self.api: PlacesAPIInterface = MockPlacesAPI(config.mock_seed)
            else:
                logger.info("使用真实Google Places API客户端")
                # 验证API密钥存在
                if not config.api_key:
                    raise ValueError("API_KEY 未在配置中找到")
                self.api: PlacesAPIInterface = GooglePlacesAPI(config.api_key)

            logger.info("API客户端初始化完成")

        except Exception as e:
            error_msg = f"API客户端初始化失败: {e}"
            logger.error(f"*** {error_msg} ***")
            raise RuntimeError(error_msg) from e

    def perform_nearby_search(
        self,
        lat: float,
        lng: float,
        radius: float,
        place_type: str,
        next_page_token: Optional[str] = None,
        refine_level: int = 0,
    ) -> Dict[str, Any]:
        """执行附近搜索，包含严格的异常处理。

        Args:
            lat: 纬度
            lng: 经度
            radius: 搜索半径（米）
            place_type: 地点类型
            next_page_token: 下一页令牌
            refine_level: 细化层级

        Returns:
            Dict[str, Any]: 搜索结果

        Raises:
            APIRateLimitError: 达到API调用限制时抛出
            NetworkError: 网络错误时抛出
            DataError: 数据解析错误时抛出
            RuntimeError: 其他严重错误时抛出
        """
        try:
            # 验证输入参数
            if not isinstance(lat, (int, float)) or not (-90 <= lat <= 90):
                raise ValueError(f"纬度必须在-90到90之间，当前值: {lat}")

            if not isinstance(lng, (int, float)) or not (-180 <= lng <= 180):
                raise ValueError(f"经度必须在-180到180之间，当前值: {lng}")

            if not isinstance(radius, (int, float)) or radius <= 0:
                raise ValueError(f"搜索半径必须为正数，当前值: {radius}")

            if not place_type or not isinstance(place_type, str):
                raise ValueError("place_type 必须为非空字符串")

            # 检查API调用限制（在计数之前检查，确保不超过限制）
            if (
                self.search_config.max_calls > 0
                and self.api_state.api_call_count >= self.search_config.max_calls
            ):
                error_msg = f"已达到最大API调用限制 {self.search_config.max_calls}。搜索停止，需要人工介入。"
                logger.error(f"*** {error_msg} ***")
                raise APIRateLimitError(error_msg)

            # 执行API调用计数
            self.api_state.increment_call_count()

            # 记录搜索请求
            logger.debug(
                f"执行附近搜索: lat={lat}, lng={lng}, radius={radius}m, type={place_type}, token={next_page_token}, level={refine_level}"
            )

            # 检查是否处于试运行模式
            if self.search_config.dry_run:
                logger.debug(
                    f"[DRY RUN] Would search: lat={lat}, lng={lng}, radius={radius}, token={next_page_token}, level={refine_level}"
                )
                return self.api.perform_nearby_search(
                    lat, lng, radius, place_type, next_page_token, refine_level
                )

            # 执行真实API调用
            try:
                data = self.api.perform_nearby_search(
                    lat, lng, radius, place_type, next_page_token, refine_level
                )

                # 验证API响应格式
                if not isinstance(data, dict):
                    error_msg = (
                        f"API返回了非字典响应: {type(data)}，需要人工介入检查API状态"
                    )
                    logger.error(f"*** {error_msg} ***")
                    raise DataError(error_msg)

                # 检查API状态 - 任何非OK/ZERO_RESULTS状态都视为需要人工介入的错误
                status = data.get("status")
                if status == "OK":
                    logger.debug("API调用成功，状态: OK")
                elif status == "ZERO_RESULTS":
                    logger.debug("API调用成功，状态: ZERO_RESULTS")
                elif status == "OVER_QUERY_LIMIT":
                    # 速率限制错误，需要等待后重试
                    logger.warning("API返回OVER_QUERY_LIMIT，触发速率限制处理")
                    self.api_state.handle_rate_limit(1.0, 60.0)

                    # 等待延迟时间
                    logger.info(f"等待 {self.api_state.current_delay} 秒后重试...")
                    time.sleep(self.api_state.current_delay)

                    # 重试一次，如果再次失败则需要人工介入
                    logger.info("重试API调用...")
                    data = self.api.perform_nearby_search(
                        lat, lng, radius, place_type, next_page_token, refine_level
                    )

                    # 检查重试后的状态
                    if data.get("status") == "OVER_QUERY_LIMIT":
                        error_msg = "速率限制重试失败，需要人工介入检查API配额和设置"
                        logger.error(f"*** {error_msg} ***")
                        raise APIRateLimitError(error_msg)
                    elif data.get("status") not in ["OK", "ZERO_RESULTS"]:
                        error_msg = f"API重试后返回非成功状态: {data.get('status')}，需要人工介入。响应详情: {data}"
                        logger.error(f"*** {error_msg} ***")
                        raise APIError(error_msg)

                    # 重试成功，重置错误状态
                    self.api_state.reset_error_state()
                    logger.info("速率限制重试成功")
                else:
                    # 其他错误状态需要人工介入
                    error_msg = (
                        f"API返回错误状态: {status}，需要人工介入检查。响应详情: {data}"
                    )
                    logger.error(f"*** {error_msg} ***")
                    raise APIError(error_msg)

                # 成功时重置错误状态
                if status in ["OK", "ZERO_RESULTS"]:
                    self.api_state.reset_error_state()

                logger.debug(f"API调用成功，返回 {len(data.get('results', []))} 个结果")
                return data

            except Exception as api_error:
                # 处理API调用过程中的异常
                if isinstance(
                    api_error, (NetworkError, DataError, APIError, APIRateLimitError)
                ):
                    # 已知的异常类型，直接重新抛出
                    raise
                else:
                    # 处理特定的异常类型以匹配测试期望
                    if isinstance(api_error, requests.exceptions.RequestException):
                        logger.error(f"API request failed: {api_error}")
                        raise NetworkError(
                            f"API request failed: {api_error}"
                        ) from api_error
                    elif isinstance(
                        api_error, (ValueError, TypeError)
                    ) and "JSON" in str(api_error):
                        logger.error(f"Failed to parse API response: {api_error}")
                        raise DataError(
                            f"Failed to parse API response: {api_error}"
                        ) from api_error
                    else:
                        # 未知异常，包装为GridRuntimeError
                        error_msg = f"Unexpected error in API call: {api_error}"
                        logger.error(f"*** {error_msg} ***")
                        raise GridRuntimeError(error_msg) from api_error

        except (ValueError, TypeError) as e:
            # 参数验证错误，直接抛出原异常
            raise

        except APIRateLimitError as e:
            # 速率限制错误，已经记录过，直接抛出
            raise

        except (NetworkError, DataError, APIError) as e:
            # API相关错误，已经记录过，直接抛出
            raise

        except Exception as e:
            # 其他未预期的错误
            raise

    def geocoding_search(self, location: str) -> Dict[str, Any]:
        """执行地理编码搜索。

        Args:
            location: 要搜索的位置名称或地址

        Returns:
            Dict[str, Any]: 地理编码结果

        Raises:
            NetworkError: 网络错误时抛出
            DataError: 数据解析错误时抛出
            RuntimeError: 其他严重错误时抛出
        """
        try:
            # 验证输入参数
            if not location or not isinstance(location, str):
                raise ValueError("location 必须为非空字符串")

            # 检查API调用限制（在计数之前检查，确保不超过限制）
            if (
                self.search_config.max_calls > 0
                and self.api_state.api_call_count >= self.search_config.max_calls
            ):
                error_msg = f"已达到最大API调用限制 {self.search_config.max_calls}。搜索停止，需要人工介入。"
                logger.error(f"*** {error_msg} ***")
                raise APIRateLimitError(error_msg)

            # 执行API调用计数
            self.api_state.increment_call_count()

            # 记录地理编码请求
            logger.debug(f"执行地理编码搜索: location={location}")

            # 执行API调用
            try:
                data = self.api.geocoding_search(location)

                # 验证响应格式
                if not isinstance(data, dict):
                    error_msg = f"地理编码API返回了非字典响应: {type(data)}，需要人工介入检查API状态"
                    logger.error(f"*** {error_msg} ***")
                    raise DataError(error_msg)

                # 检查API状态
                status = data.get("status")
                if status == "OK":
                    logger.debug("地理编码API调用成功")
                elif status == "ZERO_RESULTS":
                    logger.warning(f"地理编码未找到结果: {location}")
                else:
                    error_msg = f"地理编码API返回错误状态: {status}，需要人工介入。响应详情: {data}"
                    logger.error(f"*** {error_msg} ***")
                    raise APIError(error_msg)

                logger.debug(
                    f"地理编码成功，返回 {len(data.get('results', []))} 个结果"
                )
                return data

            except Exception as api_error:
                # 处理API调用过程中的异常
                if isinstance(api_error, (NetworkError, DataError, APIError)):
                    # 已知的异常类型，直接重新抛出
                    raise
                else:
                    # 未知异常，包装为GridRuntimeError
                    error_msg = f"Unexpected error in API call: {api_error}"
                    logger.error(f"*** {error_msg} ***")
                    raise GridRuntimeError(error_msg) from api_error

        except (ValueError, TypeError) as e:
            # 参数验证错误，直接抛出原异常
            raise

        except APIRateLimitError as e:
            # 速率限制错误，已经记录过，直接抛出
            raise

        except (NetworkError, DataError, APIError) as e:
            # API相关错误，已经记录过，直接抛出
            raise

        except Exception as e:
            # 其他未预期的错误
            raise

    def get_bounding_box(self, location: str) -> Optional[tuple]:
        """获取位置边界框。

        Args:
            location: 位置名称或地址

        Returns:
            Optional[tuple]: 边界框 (min_lat, min_lng, max_lat, max_lng) 或 None（失败时）

        Raises:
            RuntimeError: 获取边界框失败时抛出
        """
        try:
            logger.info(f"获取位置边界框: {location}")

            # 执行地理编码搜索
            geocoding_result = self.geocoding_search(location)

            # 检查结果
            if geocoding_result.get("status") != "OK":
                error_msg = (
                    f"地理编码失败，无法获取边界框: {geocoding_result.get('status')}"
                )
                logger.error(f"*** {error_msg} ***")
                raise RuntimeError(error_msg)

            results = geocoding_result.get("results", [])
            if not results:
                error_msg = f"未找到位置 '{location}' 的地理编码结果"
                logger.error(f"*** {error_msg} ***")
                raise RuntimeError(error_msg)

            # 获取第一个结果的边界框
            first_result = results[0]
            if (
                "geometry" not in first_result
                or "viewport" not in first_result["geometry"]
            ):
                error_msg = f"地理编码结果缺少几何信息: {location}"
                logger.error(f"*** {error_msg} ***")
                raise RuntimeError(error_msg)

            # 提取边界框
            viewport = first_result["geometry"]["viewport"]
            northeast = viewport["northeast"]
            southwest = viewport["southwest"]

            # 返回边界框 (min_lat, min_lng, max_lat, max_lng)
            bounding_box = (
                southwest["lat"],
                southwest["lng"],
                northeast["lat"],
                northeast["lng"],
            )

            logger.info(f"成功获取边界框: {bounding_box}")
            return bounding_box

        except Exception as e:
            error_msg = f"获取位置边界框失败: {e}，需要人工介入"
            logger.error(f"*** {error_msg} ***")
            raise GridRuntimeError(error_msg) from e

    def get_api_call_count(self) -> int:
        """获取当前API调用次数。

        Returns:
            int: API调用次数
        """
        return self.api_state.api_call_count

    def get_max_calls(self) -> int:
        """获取最大API调用次数限制。

        Returns:
            int: 最大调用次数
        """
        return self.search_config.max_calls

    def is_dry_run(self) -> bool:
        """检查是否为试运行模式。

        Returns:
            bool: 是否为试运行模式
        """
        return self.search_config.dry_run

    def get_status_summary(self) -> Dict[str, Any]:
        """获取API客户端状态摘要。

        Returns:
            Dict[str, Any]: 状态摘要信息
        """
        return {
            "api_call_count": self.api_state.api_call_count,
            "max_calls": self.search_config.max_calls,
            "dry_run": self.search_config.dry_run,
            "consecutive_errors": self.api_state.consecutive_errors,
            "current_delay": self.api_state.current_delay,
            "api_type": "Mock" if self.search_config.dry_run else "Google Places",
        }
