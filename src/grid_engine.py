"""
网格处理引擎模块

为 Google Maps Grid Search 项目提供基于广度优先搜索（BFS）的分层处理引擎。
实现非递归的迭代处理逻辑，替代旧的递归深度优先搜索。

基于 TECHNICAL_DESIGN.md 中的设计规范。
"""

import time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

from src.data_models import SearchOrchestration, GridCell, SearchStatus
from src.api_client import APIClient
from src.state_manager import StateManager
from src.grid_algorithms import (
    generate_grid_points,
    generate_mini_grid,
    meters_to_lat_degrees,
    meters_to_lng_degrees,
)
from src.logging_config import get_logger
from src.exceptions import APIError


# 获取日志记录器
logger = get_logger(__name__)


@dataclass
class ProcessingConfig:
    """处理配置"""

    max_refinement_levels: int = 4
    initial_radius: float = 1000.0
    min_refinement_radius: float = 50.0
    max_api_calls_per_cell: int = 3
    page_delay_seconds: float = 2.0  # Google Places API页面间延迟要求
    results_threshold_for_refinement: int = 20
    grid_overlap_factor: float = 0.8


@dataclass
class ProcessingStats:
    """处理统计信息"""

    total_cells: int = 0
    processed_cells: int = 0
    failed_cells: int = 0
    refined_cells: int = 0
    api_calls: int = 0
    places_found: int = 0
    processing_time: float = 0.0


class GridEngine:
    """网格处理引擎

    实现基于广度优先搜索（BFS）的分层处理逻辑。
    负责处理当前层的网格单元并规划下一层的细化单元。

    属性:
        orchestration: 搜索编排对象
        api_client: API客户端
        state_manager: 状态管理器
        config: 处理配置
    """

    def __init__(
        self,
        orchestration: SearchOrchestration,
        api_client: APIClient,
        state_manager: StateManager,
        config: Optional[ProcessingConfig] = None,
        visualization_callback=None,
    ):
        """初始化网格处理引擎

        Args:
            orchestration: 搜索编排对象
            api_client: API客户端
            state_manager: 状态管理器
            config: 处理配置
            visualization_callback: 可视化更新回调函数
        """
        self.orchestration = orchestration
        self.api_client = api_client
        self.state_manager = state_manager
        self.config = config or ProcessingConfig()
        self.visualization_callback = visualization_callback

    def process_next_layer(self) -> Tuple[bool, ProcessingStats]:
        """处理下一层

        处理当前层的所有待处理网格单元，并根据需要规划下一层。
        每个单元处理完成后立即保存状态，确保数据一致性。

        Returns:
            Tuple[bool, ProcessingStats]: (是否有更多层需要处理, 处理统计信息)
        """
        logger.info(f"开始处理第 {self.orchestration.current_layer} 层")

        # 获取当前层的待处理单元
        pending_cells = self.orchestration.get_pending_cells()

        if not pending_cells:
            logger.info(f"第 {self.orchestration.current_layer} 层没有待处理单元")
            # 标记当前层为完成
            self.orchestration.mark_layer_complete(self.orchestration.current_layer)
            # 移动到下一层
            self.orchestration.current_layer += 1
            # 保存状态
            self.state_manager.save_state(self.orchestration)
            return False, ProcessingStats()

        logger.info(
            f"第 {self.orchestration.current_layer} 层有 {len(pending_cells)} 个待处理单元"
        )

        # 初始化统计信息
        stats = ProcessingStats(total_cells=len(pending_cells))

        # 处理每个待处理单元 - 批量保存状态以提高性能
        processed_count = 0
        for cell in pending_cells:
            self._process_single_cell(cell, stats)
            processed_count += 1

            # 每处理10个单元或处理完所有单元时保存状态
            if processed_count % 10 == 0 or processed_count == len(pending_cells):
                self.state_manager.save_state(self.orchestration)
                logger.debug(f"已处理 {processed_count} 个单元，状态已保存")

            # 通知可视化更新（如果提供了回调）
            if self.visualization_callback:
                try:
                    self.visualization_callback(
                        "cell_processed", cell, self.orchestration
                    )
                except Exception as e:
                    logger.debug(f"可视化回调失败: {e}")
                    # 异常不影响主处理流程

        # 更新编排对象的指标
        self.orchestration.update_metrics(
            total_api_calls=self.orchestration.metrics.get("total_api_calls", 0)
            + stats.api_calls,
            total_places_found=self.orchestration.metrics.get("total_places_found", 0)
            + stats.places_found,
            total_cells_processed=self.orchestration.metrics.get(
                "total_cells_processed", 0
            )
            + stats.processed_cells,
        )

        # 标记当前层为完成
        self.orchestration.mark_layer_complete(self.orchestration.current_layer)

        # 检查是否需要规划下一层
        cells_needing_refinement = self.orchestration.get_cells_needing_refinement()
        has_more_layers = len(cells_needing_refinement) > 0

        if has_more_layers:
            logger.info(
                f"需要细化 {len(cells_needing_refinement)} 个单元，将规划下一层"
            )
            self._plan_next_layer(cells_needing_refinement)
            # 保存规划后的状态
            self.state_manager.save_state(self.orchestration)

            # 通知可视化细化完成更新
            if self.visualization_callback:
                try:
                    self.visualization_callback(
                        "refinement_complete", None, self.orchestration
                    )
                except Exception as e:
                    logger.debug(f"细化可视化回调失败: {e}")

        # 移动到下一层
        self.orchestration.current_layer += 1

        # 保存最终状态
        self.state_manager.save_state(self.orchestration)

        logger.info(f"第 {self.orchestration.current_layer-1} 层处理完成")
        return has_more_layers, stats

    def _process_single_cell(self, cell: GridCell, stats: ProcessingStats) -> None:
        """处理单个网格单元

        Args:
            cell: 要处理的网格单元
            stats: 处理统计信息（会被更新）
        """
        logger.debug(
            f"处理单元 {cell.cell_id} (层级: {cell.layer_id}, 位置: {cell.center_lat}, {cell.center_lng})"
        )

        # 更新单元状态为处理中
        cell.update_status(SearchStatus.PROCESSING)

        # 执行搜索
        results = self._perform_search(cell)

        # 更新API调用统计
        api_calls_used = results.get("api_calls_made", 1)
        stats.api_calls += api_calls_used

        # 处理搜索结果
        self._handle_search_results(cell, results, stats)

        # 更新统计信息
        stats.processed_cells += 1

    def _perform_search(self, cell: GridCell) -> Dict[str, Any]:
        """执行搜索

        Args:
            cell: 网格单元

        Returns:
            Dict[str, Any]: 搜索结果
        """
        place_type = self.orchestration.place_type

        # 合并所有页面的搜索结果
        all_results = {"results": [], "status": "UNKNOWN", "api_calls_made": 0}
        next_page_token = None
        api_calls_made = 0

        # 循环获取所有页面的数据，直到达到最大调用次数或没有更多页面
        while api_calls_made < self.config.max_api_calls_per_cell:
            try:
                # 验证输入参数
                if not place_type:
                    raise ValueError("place_type 不能为空")

                # 执行搜索
                page_results = self.api_client.perform_nearby_search(
                    lat=cell.center_lat,
                    lng=cell.center_lng,
                    radius=cell.search_radius,
                    place_type=place_type,
                    next_page_token=next_page_token,
                )

                api_calls_made += 1

                # 验证响应格式
                if not isinstance(page_results, dict):
                    logger.error(f"API返回了非字典响应: {type(page_results)}")
                    break

                # 检查API状态 - 实现状态优先级判断，避免状态覆盖问题
                status = page_results.get("status")
                current_status = all_results.get("status", "UNKNOWN")

                # 定义状态优先级：ERROR状态最高，OK次之，ZERO_RESULTS最低
                status_priority = {
                    "UNKNOWN": 0,
                    "ZERO_RESULTS": 1,
                    "OK": 2,
                    "INVALID_REQUEST": 3,
                    "OVER_QUERY_LIMIT": 3,
                    "REQUEST_DENIED": 3,
                    "UNKNOWN_ERROR": 3,
                }

                # 只在以下情况更新状态：
                # 1. 当前状态为UNKNOWN
                # 2. 新状态优先级高于当前状态
                # 3. 新状态为OK且当前状态为ZERO_RESULTS（提升状态）
                if (
                    current_status == "UNKNOWN"
                    or status_priority.get(status, 0)
                    > status_priority.get(current_status, 0)
                    or (status == "OK" and current_status == "ZERO_RESULTS")
                ):
                    all_results["status"] = status
                    logger.debug(f"状态更新: {current_status} -> {status}")
                elif status == "ZERO_RESULTS" and current_status == "OK":
                    # 特殊情况：如果当前是OK但新状态是ZERO_RESULTS，保持OK状态
                    logger.debug(f"状态保持为OK，忽略ZERO_RESULTS页面")
                else:
                    logger.debug(f"状态保持为{current_status}，忽略{status}页面")

                # 如果是错误状态，抛出异常
                if status_priority.get(status, 0) > 2:
                    error_message = f"API返回非成功状态: {status}，需要人工介入"
                    logger.error(f"{error_message}。响应详情: {page_results}")
                    raise APIError(error_message, status, page_results)

                # 合并结果（带去重）
                if "results" in page_results and isinstance(
                    page_results["results"], list
                ):
                    existing_place_ids = {
                        place["place_id"]
                        for place in all_results["results"]
                        if "place_id" in place
                    }

                    for place in page_results["results"]:
                        if (
                            "place_id" in place
                            and place["place_id"] not in existing_place_ids
                        ):
                            all_results["results"].append(place)
                            existing_place_ids.add(place["place_id"])

                    logger.debug(f"获取到 {len(page_results['results'])} 个结果")
                else:
                    logger.warning("API响应中没有有效结果")

                # 检查是否有下一页
                if page_results.get("next_page_token"):
                    if api_calls_made < self.config.max_api_calls_per_cell:
                        next_page_token = page_results["next_page_token"]
                        logger.debug(f"准备获取第 {api_calls_made + 1} 页数据")

                        # 只对真实API添加延迟，模拟API跳过延迟
                        if not getattr(self.api_client.config, 'dry_run', False):
                            time.sleep(self.config.page_delay_seconds)
                            logger.debug(
                                f"等待 {self.config.page_delay_seconds} 秒后获取下一页..."
                            )
                        else:
                            logger.debug("模拟模式，跳过页面延迟")
                    else:
                        logger.debug(f"已达到最大API调用限制，停止获取更多页面")
                        break
                else:
                    logger.debug("没有更多页面了")
                    break

            except Exception as e:
                logger.error(f"获取第 {api_calls_made + 1} 页数据时发生错误: {e}")
                if api_calls_made == 0:  # 第一页就失败
                    all_results["status"] = "ERROR"
                    all_results["error"] = str(e)
                    raise
                else:  # 已经有结果，继续处理已有数据
                    break

        all_results["api_calls_made"] = api_calls_made
        total_results = len(all_results["results"])
        logger.debug(
            f"单元 {cell.cell_id} 总共获取 {total_results} 个结果 (共 {api_calls_made} 次API调用)"
        )

        return all_results

    def _handle_search_results(
        self, cell: GridCell, results: Dict[str, Any], stats: ProcessingStats
    ) -> None:
        """处理搜索结果

        Args:
            cell: 网格单元
            results: 搜索结果
            stats: 处理统计信息（会被更新）
        """
        # 检查API错误状态
        if "status" in results and results["status"] == "ERROR":
            error_msg = results.get("error", "未知API错误")
            error_details = results.get("details", {})
            logger.error(f"单元 {cell.cell_id} API调用失败: {error_msg}")
            cell.set_error(error_msg)
            stats.failed_cells += 1
            raise APIError(error_msg, "API_ERROR", error_details)

        # 检查是否有API错误详情
        if "error_details" in results:
            error_details = results["error_details"]
            status = error_details.get("status", "UNKNOWN_ERROR")
            error_msg = f"API返回错误状态: {status}"
            logger.error(f"单元 {cell.cell_id} {error_msg}")
            cell.set_error(error_msg)
            stats.failed_cells += 1
            raise APIError(error_msg, status, error_details)

        # 提取地点ID
        place_ids = set()
        if "results" in results:
            for place in results["results"]:
                if "place_id" in place:
                    place_ids.add(place["place_id"])

        # 记录搜索结果（传入API调用次数以确保统计准确）
        api_calls_used = results.get("api_calls_made", 1)
        cell.record_search_results(len(place_ids), place_ids, api_calls_used)
        stats.places_found += len(place_ids)

        # 判断是否需要细化
        if (
            len(place_ids) >= self.config.results_threshold_for_refinement
            and cell.layer_id < self.config.max_refinement_levels
        ):
            cell.update_status(SearchStatus.REFINEMENT_NEEDED)
            stats.refined_cells += 1
            logger.debug(f"单元 {cell.cell_id} 需要细化 (找到 {len(place_ids)} 个地点)")
        else:
            cell.update_status(SearchStatus.SEARCH_COMPLETE)
            logger.debug(f"单元 {cell.cell_id} 处理完成 (找到 {len(place_ids)} 个地点)")

    def _plan_next_layer(self, parent_cells: List[GridCell]) -> None:
        """规划下一层

        Args:
            parent_cells: 需要细化的父单元列表
        """
        next_layer_id = self.orchestration.current_layer + 1
        logger.info(f"规划第 {next_layer_id} 层，基于 {len(parent_cells)} 个父单元")

        for parent_cell in parent_cells:
            # 计算细化半径
            refined_radius = self._calculate_refined_radius(parent_cell)

            # 生成子网格点
            child_points = self._generate_child_points(parent_cell, refined_radius)

            # 创建子网格单元
            for lat, lng in child_points:
                child_cell = GridCell(
                    cell_id="",  # 将由__post_init__生成
                    status=SearchStatus.PENDING,
                    layer_id=next_layer_id,
                    center_lat=lat,
                    center_lng=lng,
                    search_radius=refined_radius,
                    parent_id=parent_cell.cell_id,
                )

                # 添加到编排对象
                self.orchestration.add_cell(child_cell)

                # 添加到父单元的子单元列表
                parent_cell.add_child(child_cell.cell_id)

            # 标记父单元为细化完成
            parent_cell.update_status(SearchStatus.REFINEMENT_COMPLETE)

        logger.info(
            f"第 {next_layer_id} 层规划完成，共创建 {len(self.orchestration.get_layer_cells(next_layer_id))} 个单元"
        )

    def _calculate_refined_radius(self, parent_cell: GridCell) -> float:
        """计算细化半径

        Args:
            parent_cell: 父单元

        Returns:
            float: 细化半径
        """
        # 简单的半径减半策略
        refined_radius = parent_cell.search_radius / 2
        return max(refined_radius, self.config.min_refinement_radius)

    def _generate_child_points(
        self, parent_cell: GridCell, refined_radius: float
    ) -> List[Tuple[float, float]]:
        """生成子网格点

        Args:
            parent_cell: 父单元
            refined_radius: 细化半径

        Returns:
            List[Tuple[float, float]]: 子网格点列表
        """
        # 在父单元周围生成一个小型网格
        # 使用父单元的搜索半径作为边界
        parent_radius = parent_cell.search_radius

        # 生成网格点
        if parent_cell.layer_id == 0:
            # 对于根层级，生成更密集的网格
            step_meters = refined_radius * self.config.grid_overlap_factor
            points = generate_grid_points(
                bounds=(
                    parent_cell.center_lat - meters_to_lat_degrees(parent_radius),
                    parent_cell.center_lng
                    - meters_to_lng_degrees(parent_radius, parent_cell.center_lat),
                    parent_cell.center_lat + meters_to_lat_degrees(parent_radius),
                    parent_cell.center_lng
                    + meters_to_lng_degrees(parent_radius, parent_cell.center_lat),
                ),
                step_meters=step_meters,
            )
        else:
            # 对于细化层级，生成更小的网格
            step_meters = refined_radius * self.config.grid_overlap_factor
            points = generate_mini_grid(
                center_point=(parent_cell.center_lat, parent_cell.center_lng),
                area_radius=parent_radius,
                step_meters=step_meters,
            )

        # 移除父单元中心点（避免重复搜索）
        points = [
            (lat, lng)
            for lat, lng in points
            if not (
                abs(lat - parent_cell.center_lat) < 1e-6
                and abs(lng - parent_cell.center_lng) < 1e-6
            )
        ]

        return points

    def has_pending_work(self) -> bool:
        """检查是否还有待处理的工作

        Returns:
            bool: 是否还有待处理的工作
        """
        return self.orchestration.has_pending_work()

    def get_current_layer(self) -> int:
        """获取当前层

        Returns:
            int: 当前层ID
        """
        return self.orchestration.current_layer

    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息

        Returns:
            Dict[str, Any]: 处理统计信息
        """
        return {
            "current_layer": self.orchestration.current_layer,
            "completed_layers": len(self.orchestration.completed_layers),
            "total_cells": len(self.orchestration.cells),
            "metrics": self.orchestration.metrics,
        }


# 工厂函数
def create_grid_engine(
    orchestration: SearchOrchestration,
    api_client: APIClient,
    state_manager: StateManager,
    config: Optional[ProcessingConfig] = None,
) -> GridEngine:
    """创建网格处理引擎

    Args:
        orchestration: 搜索编排对象
        api_client: API客户端
        state_manager: 状态管理器
        config: 处理配置

    Returns:
        GridEngine: 配置好的网格处理引擎实例
    """
    return GridEngine(
        orchestration=orchestration,
        api_client=api_client,
        state_manager=state_manager,
        config=config,
    )
