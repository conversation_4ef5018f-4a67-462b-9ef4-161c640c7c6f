"""
简化网格处理引擎模块

重构后的网格引擎，大幅简化处理逻辑，保留所有高级功能。
"""

import time
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass

from src.data_models import SearchOrchestration, GridCell, SearchStatus
from src.api_client import APIClient
from src.state_manager import StateManager
from src.grid_algorithms import generate_mini_grid
from src.logging_config import get_logger
from src.exceptions import SearchError

logger = get_logger(__name__)


@dataclass
class ProcessingConfig:
    """简化的处理配置"""
    max_refinement_levels: int = 4
    initial_radius: float = 1000.0
    min_refinement_radius: float = 50.0
    max_api_calls_per_cell: int = 3
    page_delay_seconds: float = 2.0
    results_threshold_for_refinement: int = 20
    grid_overlap_factor: float = 0.8


@dataclass
class ProcessingStats:
    """处理统计信息"""
    total_cells: int = 0
    processed_cells: int = 0
    failed_cells: int = 0
    api_calls: int = 0
    places_found: int = 0


class GridEngine:
    """简化的网格处理引擎
    
    保留所有高级功能：
    - 断点续传
    - 递归细化
    - 实时可视化
    - 错误恢复
    """
    
    def __init__(
        self,
        orchestration: SearchOrchestration,
        api_client: APIClient,
        state_manager: StateManager,
        config: Optional[ProcessingConfig] = None,
        visualization_callback: Optional[Callable] = None,
    ):
        self.orchestration = orchestration
        self.api_client = api_client
        self.state_manager = state_manager
        self.config = config or ProcessingConfig()
        self.visualization_callback = visualization_callback

        self.stats = ProcessingStats()
        self._update_stats()

        logger.info("网格处理引擎初始化完成")
    
    def _update_stats(self):
        """更新统计信息"""
        self.stats.total_cells = len(self.orchestration.cells)
        self.stats.processed_cells = sum(
            1 for cell in self.orchestration.cells.values()
            if cell.status in [SearchStatus.SEARCH_COMPLETE, SearchStatus.REFINEMENT_COMPLETE]
        )
        self.stats.failed_cells = sum(
            1 for cell in self.orchestration.cells.values()
            if cell.status == SearchStatus.FAILED
        )
    
    def has_pending_work(self) -> bool:
        """检查是否还有待处理的工作"""
        return any(
            cell.status == SearchStatus.PENDING
            for cell in self.orchestration.cells.values()
        )
    
    def get_current_layer(self) -> int:
        """获取当前处理层级"""
        if not self.orchestration.cells:
            return 0
        return max(cell.layer_id for cell in self.orchestration.cells.values())
    
    def process_next_layer(self) -> Tuple[bool, ProcessingStats]:
        """处理下一层
        
        Returns:
            Tuple[bool, ProcessingStats]: (是否还有更多工作, 统计信息)
        """
        # 获取当前层的待处理单元
        current_layer = self.get_current_layer()
        pending_cells = [
            cell for cell in self.orchestration.cells.values()
            if cell.status == SearchStatus.PENDING and cell.layer_id == current_layer
        ]
        
        if not pending_cells:
            # 当前层没有待处理单元，检查是否需要生成下一层
            return self._plan_next_layer()
        
        logger.info(f"开始处理第 {current_layer} 层")
        logger.info(f"第 {current_layer} 层有 {len(pending_cells)} 个待处理单元")
        
        # 处理当前层的所有单元
        for i, cell in enumerate(pending_cells):
            try:
                self._process_single_cell(cell)
                
                # 每10个单元保存一次状态
                if (i + 1) % 10 == 0:
                    self.state_manager.save_state(self.orchestration)
                    logger.debug(f"已处理 {i + 1} 个单元，状态已保存")
                    
            except Exception as e:
                logger.error(f"处理单元 {cell.cell_id} 失败: {e}")
                cell.update_status(SearchStatus.FAILED)
                cell.error_message = str(e)
        
        # 保存最终状态
        self.state_manager.save_state(self.orchestration)
        
        # 更新统计信息
        self._update_stats()
        
        # 触发可视化回调
        if self.visualization_callback:
            try:
                self.visualization_callback("layer_complete", {
                    "layer": current_layer,
                    "orchestration": self.orchestration,
                    "stats": self.stats
                })
            except Exception as e:
                logger.debug(f"可视化回调失败: {e}")
        
        logger.info(f"第 {current_layer} 层处理完成")
        
        # 检查是否还有更多工作
        return self.has_pending_work(), self.stats
    
    def _process_single_cell(self, cell: GridCell, stats: Optional[ProcessingStats] = None):
        """处理单个网格单元"""
        if stats is None:
            stats = self.stats

        logger.debug(f"处理单元 {cell.cell_id} (层级: {cell.layer_id})")

        # 更新状态为处理中
        cell.update_status(SearchStatus.PROCESSING)

        try:
            # 执行搜索
            results = self._perform_search(cell)

            # 处理搜索结果
            self._handle_search_results(cell, results, stats)

            # 更新统计信息
            stats.processed_cells += 1

        except Exception as e:
            logger.error(f"单元 {cell.cell_id} 处理失败: {e}")
            cell.update_status(SearchStatus.FAILED)
            cell.error_message = str(e)
            stats.failed_cells += 1

    def _perform_search(self, cell: GridCell) -> List[Dict]:
        """执行搜索API调用"""
        all_results = []
        next_page_token = None
        api_calls_made = 0

        while api_calls_made < self.config.max_api_calls_per_cell:
            # 执行API调用
            response = self.api_client.perform_nearby_search(
                lat=cell.center_lat,
                lng=cell.center_lng,
                radius=cell.search_radius,
                place_type=self.orchestration.place_type,
                next_page_token=next_page_token,
                refine_level=cell.layer_id,
            )

            api_calls_made += 1
            self.stats.api_calls += 1

            # 处理响应
            if response.get("status") == "OK":
                results = response.get("results", [])
                all_results.extend(results)
                next_page_token = response.get("next_page_token")

                if not next_page_token:
                    break

                # 添加页面延迟（仅对真实API）
                if not getattr(self.api_client.config, 'dry_run', False):
                    time.sleep(self.config.page_delay_seconds)

            elif response.get("status") == "ZERO_RESULTS":
                break
            else:
                # API错误
                raise SearchError(f"API调用失败: {response.get('status')}")

        return all_results

    def _handle_search_results(self, cell: GridCell, results: List[Dict], stats: ProcessingStats):
        """处理搜索结果"""
        # 处理结果
        place_ids = {result.get("place_id") for result in results if result.get("place_id")}
        place_ids.discard(None)

        # 记录搜索结果
        cell.record_search_results(len(results), place_ids, cell.api_calls_count)

        # 更新统计信息
        stats.places_found += len(place_ids)

        # 判断是否需要细化
        if (len(results) >= self.config.results_threshold_for_refinement and
            cell.layer_id < self.config.max_refinement_levels and
            cell.search_radius > self.config.min_refinement_radius):

            # 标记为需要细化
            cell.update_status(SearchStatus.REFINEMENT_NEEDED)
            stats.refined_cells = getattr(stats, 'refined_cells', 0) + 1
            logger.debug(f"单元 {cell.cell_id} 需要细化 (结果数: {len(results)})")
        else:
            # 搜索完成
            cell.update_status(SearchStatus.SEARCH_COMPLETE)
            logger.debug(f"单元 {cell.cell_id} 搜索完成 (结果数: {len(results)})")

    def _plan_next_layer(self) -> Tuple[bool, ProcessingStats]:
        """规划下一层的细化"""
        # 找到需要细化的单元
        cells_to_refine = [
            cell for cell in self.orchestration.cells.values()
            if cell.status == SearchStatus.REFINEMENT_NEEDED
        ]

        if not cells_to_refine:
            logger.info("没有需要细化的单元，搜索完成")
            return False, self.stats

        logger.info(f"开始细化 {len(cells_to_refine)} 个单元")

        # 为每个需要细化的单元生成子网格
        new_cells_count = 0
        for cell in cells_to_refine:
            try:
                # 生成子网格
                new_radius = cell.search_radius * self.config.grid_overlap_factor
                mini_grid_points = generate_mini_grid(
                    center_point=(cell.center_lat, cell.center_lng),
                    area_radius=cell.search_radius,
                    step_meters=new_radius,
                )

                # 创建新的网格单元
                for i, (lat, lng) in enumerate(mini_grid_points):
                    new_cell_id = f"{cell.cell_id}_r{i}"
                    new_cell = GridCell(
                        cell_id=new_cell_id,
                        status=SearchStatus.PENDING,
                        layer_id=cell.layer_id + 1,
                        center_lat=lat,
                        center_lng=lng,
                        search_radius=new_radius,
                        parent_id=cell.cell_id,
                    )
                    self.orchestration.cells[new_cell_id] = new_cell
                    new_cells_count += 1

                # 更新父单元状态
                cell.update_status(SearchStatus.REFINEMENT_COMPLETE)

            except Exception as e:
                logger.error(f"细化单元 {cell.cell_id} 失败: {e}")
                cell.update_status(SearchStatus.FAILED)
                cell.error_message = str(e)

        logger.info(f"生成了 {new_cells_count} 个新的细化单元")

        # 保存状态
        self.state_manager.save_state(self.orchestration)

        # 触发可视化回调
        if self.visualization_callback:
            try:
                self.visualization_callback("refinement_complete", {
                    "refined_cells": len(cells_to_refine),
                    "new_cells": new_cells_count,
                    "orchestration": self.orchestration,
                    "stats": self.stats
                })
            except Exception as e:
                logger.debug(f"可视化回调失败: {e}")

        # 更新统计信息
        self._update_stats()

        return True, self.stats
