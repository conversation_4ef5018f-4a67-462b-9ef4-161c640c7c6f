"""
单元测试：APIClient 测试各种场景下的API调用处理
"""

import pytest
from unittest.mock import Mock, patch
from src.api_client import APIClient
from src.exceptions import NetworkError, DataError, GridRuntimeError, APIRateLimitError


class ApiConfig(dict):
    def __init__(self):
        super().__init__()
        self.API_KEY = "test_key"
        self["API_KEY"] = "test_key"
        self.base_delay = 1.0
        self.max_delay = 60.0
        self["BASE_DELAY"] = 1.0
        self["MAX_DELAY"] = 60.0

    def get(self, key, default=None):
        return self[key] if key in self else default


class ApiState:
    def __init__(self):
        self.api_call_count = 0
        self.current_delay = 1.0
        self.consecutive_errors = 0

    def increment_call_count(self):
        self.api_call_count += 1

    def handle_rate_limit(self, base_delay, max_delay):
        self.consecutive_errors += 1
        self.current_delay = min(base_delay * (2**self.consecutive_errors), max_delay)

    def reset_error_state(self):
        self.consecutive_errors = max(0, self.consecutive_errors - 1)
        self.current_delay = max(1.0, self.current_delay / 2)


class SearchConfig:
    def __init__(
        self,
        target_location,
        place_type,
        dry_run,
        test_area=None,
        max_calls=0,
        visualize=False,
        param_test=False,
        mock_seed=None,
        work_dir=None,
    ):
        self.target_location = target_location
        self.place_type = place_type
        self.dry_run = dry_run
        self.test_area = test_area
        self.max_calls = max_calls
        self.visualize = visualize
        self.param_test = param_test
        self.mock_seed = mock_seed
        self.work_dir = work_dir
        self.test_areas = {}


class FilePaths:
    def __init__(self):
        self.work_dir = "test_work"
        self.output_file = "output.html"
        self.summary_csv = "summary.csv"
        self.detailed_data_dir = "detailed_place_data"

    def update_work_dir(self, work_dir, dry_run):
        """更新工作目录"""
        self.work_dir = work_dir
        # 在实际实现中，这里会更新所有相关路径
        # 为了测试目的，我们保持简化


@pytest.fixture
def mock_config():
    """模拟配置数据"""
    return {
        "API_KEY": "test_key",
        "BASE_NEARBY_SEARCH_URL": "https://maps.googleapis.com/maps/api/place/nearbysearch/json",
        "GEOCODING_URL": "https://maps.googleapis.com/maps/api/geocode/json",
        "PLACE_DETAILS_URL": "https://maps.googleapis.com/maps/api/place/details/json",
        "BASE_DELAY": 1.0,
        "MAX_DELAY": 60.0,
        "POINT_STATE_PENDING": "pending",
        "POINT_STATE_REFINING": "refining",
        "POINT_STATE_COMPLETE": "complete",
        "SUBDIVISION_THRESHOLD": 20,
        "MAX_RADIUS": 50000,
        "NEARBY_SEARCH_SINGLE_PAGE_SIZE": 20,
        "NEARBY_SEARCH_ACTUAL_LIMIT": 60,
        "MINI_RADIUS_FACTOR": 2.0,
        "MINI_GRID_OVERLAP_FACTOR": 0.5,
        "RECURSIVE_REFINEMENT_ENABLED": True,
        "MAX_REFINEMENT_LEVELS": 5,
        "MIN_REFINEMENT_RADIUS": 50,
        "INITIAL_RADIUS_FACTOR": 1.0,
        "GRID_STEP_FACTOR": 1.5,
        "TEST_AREAS": {},
    }


@pytest.fixture
def api_client_factory(mock_config):
    """创建API客户端工厂函数"""

    def _create_fixtures():
        @patch.dict("os.environ", {"GOOGLE_MAPS_API_KEY": "test_key"})
        def _inner():
            # 直接创建配置对象，不再需要 load_config
            from src.config import ApplicationConfig
            
            # 创建模拟的命令行参数
            class MockArgs:
                def __init__(self):
                    self.work_dir = "test_work"
                    self.dry_run = False
                    self.test_area = None
                    self.mock_seed = 42
                    self.max_calls = 100
                    self.visualize = False
                    self.param_test = False
                    self.place_type = "restaurant"
                    self.location = "Test Location"
                    self.log_level = "INFO"
                    self.initial_radius = None
                    self.initial_grid_step = None

            # 创建模拟的算法配置
            algorithm_config = {
                "search": {
                    "max_radius": 50000,
                    "min_refinement_radius": 250,
                    "max_refinement_levels": 4,
                    "initial_radius_factor": 0.25,
                    "grid_step_factor": 0.8,
                    "subdivision_threshold": 45,
                    "mini_radius_factor": 3.0,
                    "mini_grid_overlap_factor": 1.0,
                },
                "api": {
                    "nearby_search_single_page_size": 20,
                    "nearby_search_actual_limit": 60,
                }
            }

            config = ApplicationConfig.from_args(MockArgs(), "test_key", algorithm_config)

            api_state = ApiState()
            search_config = config  # 现在配置对象就是 search_config
            file_paths = FilePaths()
            file_paths.update_work_dir("test_work", False)

            return {
                "config": config,
                "api_state": api_state,
                "search_config": search_config,
                "file_paths": file_paths,
            }

        return _inner()

    return _create_fixtures


class TestAPIClient:
    """APIClient类的单元测试"""

    @patch.dict("os.environ", {"GOOGLE_MAPS_API_KEY": "test_key"})
    def test_init_with_dry_run(self, mock_config):
        """测试初始化时使用dry_run模式"""
        
        # 创建配置对象
        from src.config import ApplicationConfig
        
        class MockArgs:
            def __init__(self):
                self.work_dir = "test_work"
                self.dry_run = True  # 启用dry_run
                self.test_area = None
                self.mock_seed = 42
                self.max_calls = 100
                self.visualize = False
                self.param_test = False
                self.place_type = "restaurant"
                self.location = "Test Location"
                self.log_level = "INFO"
                self.initial_radius = None
                self.initial_grid_step = None

        algorithm_config = {
            "search": {
                "max_radius": 50000,
                "min_refinement_radius": 250,
                "max_refinement_levels": 4,
                "initial_radius_factor": 0.25,
                "grid_step_factor": 0.8,
                "subdivision_threshold": 45,
                "mini_radius_factor": 3.0,
                "mini_grid_overlap_factor": 1.0,
            },
            "api": {
                "nearby_search_single_page_size": 20,
                "nearby_search_actual_limit": 60,
            }
        }

        config = ApplicationConfig.from_args(MockArgs(), "test_key", algorithm_config)
        api_state = ApiState()
        search_config = config
        file_paths = FilePaths()
        file_paths.update_work_dir("test_work", False)

        client = APIClient(config, api_state, search_config, file_paths)

        # 验证使用了Mock API
        assert hasattr(client.api, "__class__")
        api_type = str(type(client.api))
        assert "Mock" in api_type, f"Expected Mock API, got {api_type}"

    @patch.dict("os.environ", {"GOOGLE_MAPS_API_KEY": "test_key"})
    def test_init_without_dry_run(self, mock_config):
        """测试初始化时不使用dry_run模式"""
        
        # 创建配置对象
        from src.config import ApplicationConfig
        
        class MockArgs:
            def __init__(self):
                self.work_dir = "test_work"
                self.dry_run = False  # 禁用dry_run
                self.test_area = None
                self.mock_seed = 42
                self.max_calls = 100
                self.visualize = False
                self.param_test = False
                self.place_type = "restaurant"
                self.location = "Test Location"
                self.log_level = "INFO"
                self.initial_radius = None
                self.initial_grid_step = None

        algorithm_config = {
            "search": {
                "max_radius": 50000,
                "min_refinement_radius": 250,
                "max_refinement_levels": 4,
                "initial_radius_factor": 0.25,
                "grid_step_factor": 0.8,
                "subdivision_threshold": 45,
                "mini_radius_factor": 3.0,
                "mini_grid_overlap_factor": 1.0,
            },
            "api": {
                "nearby_search_single_page_size": 20,
                "nearby_search_actual_limit": 60,
            }
        }

        config = ApplicationConfig.from_args(MockArgs(), "test_key", algorithm_config)
        api_state = ApiState()
        search_config = config
        file_paths = FilePaths()
        file_paths.update_work_dir("test_work", False)

        client = APIClient(config, api_state, search_config, file_paths)

        # 验证使用了Google API
        assert hasattr(client.api, "__class__")
        api_type = str(type(client.api))
        assert "Google" in api_type, f"Expected Google API, got {api_type}"

    def test_perform_nearby_search_api_call_count_increment(self, api_client_factory):
        """测试perform_nearby_search会增加API调用计数"""
        fixtures = api_client_factory()
        fixtures["search_config"].dry_run = False

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )
        client.api.perform_nearby_search = Mock(return_value={"status": "OK"})

        initial_count = fixtures["api_state"].api_call_count
        client.perform_nearby_search(52.52, 13.40, 1000, "restaurant")

        # 验证调用计数增加了
        assert fixtures["api_state"].api_call_count == initial_count + 1

    def test_perform_nearby_search_max_calls_limit(self, api_client_factory):
        """测试当达到最大API调用限制时抛出异常"""
        fixtures = api_client_factory()
        fixtures["search_config"].max_calls = 1

        # 预先设置API调用计数为最大值，这样下次调用就会触发限制
        fixtures["api_state"].api_call_count = 1

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )

        # 直接调用应该抛出异常，因为max_calls=1且已经达到限制
        with pytest.raises(APIRateLimitError, match="已达到最大API调用限制"):
            client.perform_nearby_search(52.52, 13.40, 1000, "restaurant")

    def test_perform_nearby_search_dry_run_logging(self, api_client_factory):
        """测试dry_run模式下的日志记录"""
        fixtures = api_client_factory()
        fixtures["search_config"].dry_run = True

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )

        # 模拟API返回
        client.api.perform_nearby_search = Mock(return_value={"status": "OK"})

        with patch("src.api_client.logger") as mock_logger:
            client.perform_nearby_search(52.52, 13.40, 1000, "restaurant")

            # 验证记录了dry run日志
            mock_logger.debug.assert_called_with(
                "[DRY RUN] Would search: lat=52.52, lng=13.4, radius=1000, token=None, level=0"
            )

    def test_perform_nearby_search_rate_limit_handling(self, api_client_factory):
        """测试速率限制处理"""
        fixtures = api_client_factory()
        fixtures["search_config"].dry_run = False

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )

        # 第一次调用返回速率限制，第二次调用成功
        client.api.perform_nearby_search = Mock(
            side_effect=[{"status": "OVER_QUERY_LIMIT"}, {"status": "OK"}]
        )

        with patch("time.sleep") as mock_sleep, patch(
            "src.api_client.logger"
        ) as mock_logger:

            result = client.perform_nearby_search(52.52, 13.40, 1000, "restaurant")

            # 验证结果
            assert result["status"] == "OK"

            # 验证调用了sleep进行延迟
            mock_sleep.assert_called_once()

            # 验证记录了速率限制日志
            mock_logger.info.assert_called()

    def test_perform_nearby_search_network_error_handling(self, api_client_factory):
        """测试网络错误处理"""
        fixtures = api_client_factory()
        fixtures["search_config"].dry_run = False

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )

        # 模拟网络错误
        import requests

        client.api.perform_nearby_search = Mock(
            side_effect=requests.exceptions.RequestException("Network error")
        )

        with patch("src.api_client.logger") as mock_logger:
            # 验证抛出了NetworkError异常
            with pytest.raises(NetworkError) as exc_info:
                client.perform_nearby_search(52.52, 13.40, 1000, "restaurant")

            # 验证异常消息
            assert "API request failed: Network error" in str(exc_info.value)

            # 验证记录了错误日志
            mock_logger.error.assert_called()

    def test_perform_nearby_search_value_error_handling(self, api_client_factory):
        """测试数据解析错误处理"""
        fixtures = api_client_factory()
        fixtures["search_config"].dry_run = False

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )

        # 模拟值错误
        client.api.perform_nearby_search = Mock(side_effect=ValueError("Invalid JSON"))

        with patch("src.api_client.logger") as mock_logger:
            # 验证抛出了DataError异常
            with pytest.raises(DataError) as exc_info:
                client.perform_nearby_search(52.52, 13.40, 1000, "restaurant")

            # 验证异常消息
            assert "Failed to parse API response: Invalid JSON" in str(exc_info.value)

            # 验证记录了错误日志
            mock_logger.error.assert_called()

    def test_perform_nearby_search_unexpected_error_handling(self, api_client_factory):
        """测试未知错误处理"""
        fixtures = api_client_factory()
        fixtures["search_config"].dry_run = False

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )

        # 模拟未知错误
        client.api.perform_nearby_search = Mock(
            side_effect=Exception("Unexpected error")
        )

        with patch("src.api_client.logger") as mock_logger:
            # 验证抛出了GridRuntimeError异常
            with pytest.raises(GridRuntimeError) as exc_info:
                client.perform_nearby_search(52.52, 13.40, 1000, "restaurant")

            # 验证异常消息
            assert "Unexpected error in API call: Unexpected error" in str(
                exc_info.value
            )

            # 验证记录了错误日志
            mock_logger.error.assert_called()

    def test_geocoding_search_api_call_count_increment(self, api_client_factory):
        """测试geocoding_search会增加API调用计数"""
        fixtures = api_client_factory()
        fixtures["search_config"].dry_run = False

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )

        # 模拟API返回成功
        client.api.geocoding_search = Mock(return_value={"status": "OK"})

        initial_count = fixtures["api_state"].api_call_count
        client.geocoding_search("Berlin")

        # 验证调用计数增加了
        assert fixtures["api_state"].api_call_count == initial_count + 1

    def test_geocoding_search_max_calls_limit(self, api_client_factory):
        """测试geocoding_search当达到最大API调用限制时抛出异常"""
        fixtures = api_client_factory()
        fixtures["search_config"].max_calls = 1

        # 预先设置API调用计数为最大值，这样下次调用就会触发限制
        fixtures["api_state"].api_call_count = 1

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )

        # 直接调用应该抛出异常，因为max_calls=1且已经达到限制
        with pytest.raises(APIRateLimitError, match="已达到最大API调用限制"):
            client.geocoding_search("Berlin")

    def test_perform_nearby_search_with_next_page_token(self, api_client_factory):
        """测试使用next_page_token的附近搜索"""
        fixtures = api_client_factory()
        fixtures["search_config"].dry_run = False

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )

        # 模拟API返回成功
        client.api.perform_nearby_search = Mock(return_value={"status": "OK"})

        client.perform_nearby_search(
            52.52,
            13.40,
            1000,
            "restaurant",
            next_page_token="test_token",
            refine_level=2,
        )

        # 验证API调用使用了正确的参数
        client.api.perform_nearby_search.assert_called_once_with(
            52.52, 13.40, 1000, "restaurant", "test_token", 2
        )

    def test_successful_search_resets_error_state(self, api_client_factory):
        """测试成功搜索会重置错误状态"""
        fixtures = api_client_factory()
        fixtures["search_config"].dry_run = False

        client = APIClient(
            fixtures["config"],
            fixtures["api_state"],
            fixtures["search_config"],
            fixtures["file_paths"],
        )

        # 模拟API返回成功
        client.api.perform_nearby_search = Mock(return_value={"status": "OK"})

        # 设置错误状态
        initial_delay = 8.0
        initial_errors = 3
        fixtures["api_state"].current_delay = initial_delay
        fixtures["api_state"].consecutive_errors = initial_errors

        client.perform_nearby_search(52.52, 13.40, 1000, "restaurant")

        # 验证错误状态被重置（减少而不是完全重置）
        assert fixtures["api_state"].consecutive_errors == initial_errors - 1
        assert fixtures["api_state"].current_delay == max(1.0, initial_delay / 2)

    def test_api_error_construction(self):
        """测试APIError的构造函数"""
        from src.exceptions import APIError, GridSearchError

        # 测试基本构造
        error_msg = "Test error message"
        error_code = "TEST_CODE"
        error_details = {"status": "INVALID_REQUEST", "results": []}

        error = APIError(error_msg, error_code, error_details)

        # 验证属性
        assert error.message == error_msg
        assert error.error_code == error_code
        assert error.details == error_details

        # 验证字符串表示
        assert str(error) == f"[{error_code}] {error_msg}"

        # 验证继承关系
        assert isinstance(error, APIError)
        assert isinstance(error, GridSearchError)
