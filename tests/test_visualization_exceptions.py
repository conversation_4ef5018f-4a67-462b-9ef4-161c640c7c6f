"""
可视化异常处理模块测试

测试可视化异常类和装饰器的功能。
"""

import pytest
from unittest.mock import patch

from src.visualization_exceptions import (
    VisualizationError,
    VisualizationInitializationError,
    VisualizationUpdateError,
    VisualizationConfigurationError,
    VisualizationFileError,
    handle_visualization_errors,
    safe_visualization_operation,
    create_visualization_error_context,
    log_visualization_error,
    is_recoverable_error,
    get_error_recovery_suggestion,
)


class TestVisualizationError:
    """可视化基础异常测试类"""

    def test_basic_error_creation(self):
        """测试基础异常创建"""
        error = VisualizationError("Test error message")

        assert str(error) == "[VISUALIZATION_ERROR] Test error message"
        assert error.message == "Test error message"
        assert error.error_code == "VISUALIZATION_ERROR"
        assert error.original_error is None

    def test_error_with_custom_error_code(self):
        """测试带自定义错误码的异常"""
        error = VisualizationError("Test error", "CUSTOM_ERROR")

        assert str(error) == "[CUSTOM_ERROR] Test error"
        assert error.error_code == "CUSTOM_ERROR"

    def test_error_with_original_error(self):
        """测试带原始错误的异常"""
        original_error = ValueError("Original error")
        error = VisualizationError("Wrapper error", original_error=original_error)

        assert (
            str(error)
            == "[VISUALIZATION_ERROR] Wrapper error (原始错误: Original error)"
        )
        assert error.original_error == original_error

    def test_error_with_code_and_original(self):
        """测试同时带错误码和原始错误的异常"""
        original_error = TypeError("Type error")
        error = VisualizationError("Wrapper error", "WRAPPER_ERROR", original_error)

        expected = "[WRAPPER_ERROR] Wrapper error (原始错误: Type error)"
        assert str(error) == expected
        assert error.error_code == "WRAPPER_ERROR"
        assert error.original_error == original_error


class TestVisualizationInitializationError:
    """可视化初始化异常测试类"""

    def test_basic_init_error(self):
        """测试基础初始化异常"""
        error = VisualizationInitializationError("Init failed")

        assert str(error) == "[VISUALIZATION_INIT_ERROR] Init failed"
        assert error.error_code == "VISUALIZATION_INIT_ERROR"
        assert error.service_type is None

    def test_init_error_with_service_type(self):
        """测试带服务类型的初始化异常"""
        error = VisualizationInitializationError("Folium init failed", "folium")

        assert str(error) == "[VISUALIZATION_FOLIUM_INIT_ERROR] Folium init failed"
        assert error.error_code == "VISUALIZATION_FOLIUM_INIT_ERROR"
        assert error.service_type == "folium"

    def test_init_error_with_original_error(self):
        """测试带原始错误的初始化异常"""
        original_error = ImportError("Module not found")
        error = VisualizationInitializationError(
            "Import failed", "service", original_error
        )

        expected = "[VISUALIZATION_SERVICE_INIT_ERROR] Import failed (原始错误: Module not found)"
        assert str(error) == expected
        assert error.original_error == original_error


class TestVisualizationUpdateError:
    """可视化更新异常测试类"""

    def test_basic_update_error(self):
        """测试基础更新异常"""
        error = VisualizationUpdateError("Update failed")

        assert str(error) == "[VISUALIZATION_UPDATE_ERROR] Update failed"
        assert error.error_code == "VISUALIZATION_UPDATE_ERROR"
        assert error.update_type is None

    def test_update_error_with_type(self):
        """测试带更新类型的异常"""
        error = VisualizationUpdateError("Cell update failed", "cell_processed")

        assert (
            str(error)
            == "[VISUALIZATION_CELL_PROCESSED_UPDATE_ERROR] Cell update failed"
        )
        assert error.error_code == "VISUALIZATION_CELL_PROCESSED_UPDATE_ERROR"
        assert error.update_type == "cell_processed"

    def test_update_error_with_original_error(self):
        """测试带原始错误的更新异常"""
        original_error = RuntimeError("Runtime error")
        error = VisualizationUpdateError(
            "Runtime update failed", "runtime", original_error
        )

        expected = "[VISUALIZATION_RUNTIME_UPDATE_ERROR] Runtime update failed (原始错误: Runtime error)"
        assert str(error) == expected
        assert error.original_error == original_error


class TestVisualizationConfigurationError:
    """可视化配置异常测试类"""

    def test_basic_config_error(self):
        """测试基础配置异常"""
        error = VisualizationConfigurationError("Config error")

        assert str(error) == "[VISUALIZATION_CONFIG_ERROR] Config error"
        assert error.error_code == "VISUALIZATION_CONFIG_ERROR"
        assert error.config_key is None

    def test_config_error_with_key(self):
        """测试带配置键的异常"""
        error = VisualizationConfigurationError("Invalid value", "update_interval")

        assert (
            str(error) == "[VISUALIZATION_UPDATE_INTERVAL_CONFIG_ERROR] Invalid value"
        )
        assert error.error_code == "VISUALIZATION_UPDATE_INTERVAL_CONFIG_ERROR"
        assert error.config_key == "update_interval"

    def test_config_error_with_original_error(self):
        """测试带原始错误的配置异常"""
        original_error = ValueError("Invalid number")
        error = VisualizationConfigurationError(
            "Number error", "max_file_count", original_error
        )

        expected = "[VISUALIZATION_MAX_FILE_COUNT_CONFIG_ERROR] Number error (原始错误: Invalid number)"
        assert str(error) == expected
        assert error.original_error == original_error


class TestVisualizationFileError:
    """可视化文件异常测试类"""

    def test_basic_file_error(self):
        """测试基础文件异常"""
        error = VisualizationFileError("File error")

        assert str(error) == "[VISUALIZATION_FILE_ERROR] File error"
        assert error.error_code == "VISUALIZATION_FILE_ERROR"
        assert error.file_path is None
        assert error.operation is None

    def test_file_error_with_path(self):
        """测试带文件路径的异常"""
        error = VisualizationFileError("Path error", "/path/to/file")

        assert str(error) == "[VISUALIZATION_FILE_ERROR] Path error"
        assert error.file_path == "/path/to/file"
        assert error.operation is None

    def test_file_error_with_operation(self):
        """测试带操作类型的异常"""
        error = VisualizationFileError("Write error", operation="write")

        assert str(error) == "[VISUALIZATION_WRITE_FILE_ERROR] Write error"
        assert error.error_code == "VISUALIZATION_WRITE_FILE_ERROR"
        assert error.operation == "write"

    def test_file_error_with_all_params(self):
        """测试带所有参数的文件异常"""
        original_error = IOError("Permission denied")
        error = VisualizationFileError(
            "Complete error", "/path/to/file", "read", original_error
        )

        expected = "[VISUALIZATION_READ_FILE_ERROR] Complete error (原始错误: Permission denied)"
        assert str(error) == expected
        assert error.file_path == "/path/to/file"
        assert error.operation == "read"
        assert error.original_error == original_error


class TestHandleVisualizationErrors:
    """可视化错误处理装饰器测试类"""

    def test_handle_visualization_errors_success(self):
        """测试成功执行的情况"""

        @handle_visualization_errors
        def successful_function():
            return "success"

        result = successful_function()
        assert result == "success"

    def test_handle_visualization_errors_with_visualization_error(self):
        """测试处理可视化异常的情况"""

        @handle_visualization_errors
        def function_with_viz_error():
            raise VisualizationError("Test viz error")

        with patch("src.visualization_exceptions.logger") as mock_logger:
            result = function_with_viz_error()
            assert result is None
            mock_logger.warning.assert_called()

    def test_handle_visualization_errors_with_import_error(self):
        """测试处理导入异常的情况"""

        @handle_visualization_errors
        def function_with_import_error():
            raise ImportError("Module not found")

        with patch("src.visualization_exceptions.logger") as mock_logger:
            result = function_with_import_error()
            assert result is None
            mock_logger.info.assert_called()

    def test_handle_visualization_errors_with_permission_error(self):
        """测试处理权限异常的情况"""

        @handle_visualization_errors
        def function_with_permission_error():
            raise PermissionError("Permission denied")

        with patch("src.visualization_exceptions.logger") as mock_logger:
            result = function_with_permission_error()
            assert result is None
            mock_logger.warning.assert_called()

    def test_handle_visualization_errors_with_file_not_found_error(self):
        """测试处理文件未找到异常的情况"""

        @handle_visualization_errors
        def function_with_file_error():
            raise FileNotFoundError("File not found")

        with patch("src.visualization_exceptions.logger") as mock_logger:
            result = function_with_file_error()
            assert result is None
            mock_logger.info.assert_called()

    def test_handle_visualization_errors_with_value_error(self):
        """测试处理值异常并转换为配置异常的情况"""

        @handle_visualization_errors
        def function_with_value_error():
            raise ValueError("Invalid value")

        with pytest.raises(VisualizationConfigurationError) as exc_info:
            function_with_value_error()

        assert "参数错误" in str(exc_info.value)

    def test_handle_visualization_errors_with_type_error(self):
        """测试处理类型异常并转换为配置异常的情况"""

        @handle_visualization_errors
        def function_with_type_error():
            raise TypeError("Invalid type")

        with pytest.raises(VisualizationConfigurationError) as exc_info:
            function_with_type_error()

        assert "参数错误" in str(exc_info.value)

    def test_handle_visualization_errors_with_generic_error(self):
        """测试处理通用异常的情况"""

        @handle_visualization_errors
        def function_with_generic_error():
            raise RuntimeError("Generic error")

        with pytest.raises(VisualizationError) as exc_info:
            function_with_generic_error()

        assert "未知错误" in str(exc_info.value)


class TestSafeVisualizationOperation:
    """安全可视化操作装饰器测试类"""

    def test_safe_operation_success(self):
        """测试成功执行的情况"""

        @safe_visualization_operation(default_return="default")
        def successful_function():
            return "success"

        result = successful_function()
        assert result == "success"

    def test_safe_operation_with_error(self):
        """测试出错时返回默认值的情况"""

        @safe_visualization_operation(default_return="default")
        def error_function():
            raise ValueError("Some error")

        with patch("src.visualization_exceptions.logger") as mock_logger:
            result = error_function()
            assert result == "default"
            mock_logger.warning.assert_called()

    def test_safe_operation_with_custom_log_level(self):
        """测试自定义日志级别"""

        @safe_visualization_operation(default_return=False, log_level="error")
        def error_function():
            raise ValueError("Some error")

        with patch("src.visualization_exceptions.logger") as mock_logger:
            result = error_function()
            assert result is False
            mock_logger.error.assert_called()

    def test_safe_operation_with_debug_log_level(self):
        """测试调试日志级别"""

        @safe_visualization_operation(default_return=None, log_level="debug")
        def error_function():
            raise ValueError("Some error")

        with patch("src.visualization_exceptions.logger") as mock_logger:
            result = error_function()
            assert result is None
            mock_logger.debug.assert_called()


class TestVisualizationErrorContext:
    """可视化错误上下文工具函数测试类"""

    def test_create_visualization_error_context_basic(self):
        """测试基础错误上下文创建"""
        context = create_visualization_error_context("test_operation")

        assert context["operation"] == "test_operation"
        assert "timestamp" in context
        assert "unix_timestamp" in context
        assert isinstance(context["timestamp"], str)
        assert isinstance(context["unix_timestamp"], float)

    def test_create_visualization_error_context_with_metadata(self):
        """测试带元数据的错误上下文创建"""
        context = create_visualization_error_context(
            "update_map", file_path="/path/to/map.html", cell_count=42
        )

        assert context["operation"] == "update_map"
        assert context["file_path"] == "/path/to/map.html"
        assert context["cell_count"] == 42
        assert "timestamp" in context
        assert "unix_timestamp" in context


class TestLogVisualizationError:
    """可视化错误记录测试类"""

    def test_log_visualization_error_with_known_error(self):
        """测试记录已知可视化错误"""
        error = VisualizationError("Test error")
        context = {"operation": "test"}

        with patch("src.visualization_exceptions.logger") as mock_logger:
            log_visualization_error(error, context)
            mock_logger.warning.assert_called()

    def test_log_visualization_error_with_unknown_error(self):
        """测试记录未知可视化错误"""
        error = RuntimeError("Unknown error")
        context = {"operation": "test"}

        with patch("src.visualization_exceptions.logger") as mock_logger:
            log_visualization_error(error, context)
            mock_logger.error.assert_called()

    def test_log_visualization_error_without_context(self):
        """测试不带上下文的错误记录"""
        error = VisualizationError("Test error")

        with patch("src.visualization_exceptions.logger") as mock_logger:
            log_visualization_error(error)
            mock_logger.warning.assert_called()

    def test_log_visualization_error_context_structure(self):
        """测试错误上下文结构"""
        error = VisualizationError("Test error")
        context = {"operation": "test", "key": "value"}

        with patch("src.visualization_exceptions.logger") as mock_logger:
            log_visualization_error(error, context)

            # 验证调用的参数
            args, kwargs = mock_logger.warning.call_args
            logged_info = args[0]

            assert "error_type" in logged_info
            assert "error_message" in logged_info
            assert "error_code" in logged_info
            assert "context" in logged_info


class TestErrorRecoveryTools:
    """错误恢复工具函数测试类"""

    def test_is_recoverable_error_with_recoverable_errors(self):
        """测试可恢复错误的判断"""
        recoverable_errors = [
            ImportError("Import error"),
            PermissionError("Permission error"),
            FileNotFoundError("File not found"),
            RuntimeError("Runtime error"),
            IOError("IO error"),
            OSError("OS error"),
        ]

        for error in recoverable_errors:
            assert is_recoverable_error(error) is True

    def test_is_recoverable_error_with_unrecoverable_errors(self):
        """测试不可恢复错误的判断"""
        unrecoverable_errors = [
            ValueError("Value error"),
            TypeError("Type error"),
            MemoryError("Memory error"),
            SyntaxError("Syntax error"),
            IndentationError("Indentation error"),
        ]

        for error in unrecoverable_errors:
            assert is_recoverable_error(error) is False

    def test_get_error_recovery_suggestion_import_error(self):
        """测试导入错误的恢复建议"""
        error = ImportError("Module not found")
        suggestion = get_error_recovery_suggestion(error)
        assert "可视化库" in suggestion
        assert "安装" in suggestion

    def test_get_error_recovery_suggestion_permission_error(self):
        """测试权限错误的恢复建议"""
        error = PermissionError("Permission denied")
        suggestion = get_error_recovery_suggestion(error)
        assert "文件权限" in suggestion

    def test_get_error_recovery_suggestion_file_not_found_error(self):
        """测试文件未找到错误的恢复建议"""
        error = FileNotFoundError("File not found")
        suggestion = get_error_recovery_suggestion(error)
        assert "文件路径" in suggestion
        assert "创建" in suggestion

    def test_get_error_recovery_suggestion_config_error(self):
        """测试配置错误的恢复建议"""
        error = VisualizationConfigurationError("Config error")
        suggestion = get_error_recovery_suggestion(error)
        assert "配置参数" in suggestion

    def test_get_error_recovery_suggestion_init_error(self):
        """测试初始化错误的恢复建议"""
        error = VisualizationInitializationError("Init error")
        suggestion = get_error_recovery_suggestion(error)
        assert "可视化服务" in suggestion
        assert "初始化" in suggestion

    def test_get_error_recovery_suggestion_update_error(self):
        """测试更新错误的恢复建议"""
        error = VisualizationUpdateError("Update error")
        suggestion = get_error_recovery_suggestion(error)
        assert "可视化更新" in suggestion

    def test_get_error_recovery_suggestion_file_error(self):
        """测试文件错误的恢复建议"""
        error = VisualizationFileError("File error")
        suggestion = get_error_recovery_suggestion(error)
        assert "文件路径" in suggestion
        assert "操作权限" in suggestion

    def test_get_error_recovery_suggestion_generic_error(self):
        """测试通用错误的恢复建议"""
        error = RuntimeError("Generic error")
        suggestion = get_error_recovery_suggestion(error)
        assert "系统状态" in suggestion
        assert "配置参数" in suggestion
