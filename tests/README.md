# 测试套件说明

本目录包含了 Google Maps Grid Search 项目的完整测试套件。

## 测试类型

### 1. 单元测试
针对各个模块的独立功能测试：
- `test_api_client.py` - API客户端测试
- `test_data_models.py` - 数据模型测试
- `test_grid_algorithms.py` - 网格算法测试
- `test_grid_engine.py` - 网格处理引擎测试
- `test_state_manager.py` - 状态管理器测试
- `test_visualization.py` - 可视化模块测试
- `test_report_generator.py` - 报告生成器测试

### 2. 集成测试
测试模块间的协作和端到端功能：
- `test_integration.py` - 集成测试，包括中断和恢复功能

### 3. 性能测试
基准测试新旧架构的性能对比：
- `test_performance.py` - 性能基准测试

## 运行测试

### 运行所有测试
```bash
python -m pytest tests/ -v
```

### 运行特定测试
```bash
# 运行单元测试
python -m pytest tests/test_data_models.py -v

# 运行集成测试
python -m pytest tests/test_integration.py -v

# 运行性能测试
python -m tests/test_performance
```

## 性能基准测试

性能基准测试用于对比新旧架构的性能表现。

### 运行性能测试
```bash
# 运行完整的性能基准测试
python -m tests/test_performance

# 指定测试区域
python -m tests/test_performance --test-areas alexanderplatz tiergarten

# 指定迭代次数
python -m tests/test_performance --iterations 5

# 指定输出目录
python -m tests/test_performance --output-dir my_benchmark_results
```

### 测试指标
1. **处理时间** - 总执行时间
2. **内存使用** - 峰值内存占用
3. **API调用次数** - 总API调用次数
4. **地点发现数量** - 发现的唯一地点ID数量

### 测试区域
- `alexanderplatz` - 密集城区
- `tiergarten` - 稀疏公园区域
- `kreuzberg` - 混合密度区域

测试结果将保存在 `benchmark_results` 目录中，包括详细的JSON数据和Markdown格式的报告。