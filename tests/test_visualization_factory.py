"""
可视化工厂模块测试

测试 VisualizationFactory 类的功能。
"""

import tempfile
from unittest.mock import patch
from pathlib import Path
import importlib.util

from src.visualization_factory import VisualizationFactory
from src.visualization_service import VisualizationService


class TestVisualizationFactory:
    """可视化工厂测试类"""

    def test_create_service_success(self):
        """测试成功创建可视化服务"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 模拟依赖可用
            with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
                service = VisualizationFactory.create_service(temp_dir)

                assert service is not None
                assert isinstance(service, VisualizationService)
                # 在macOS上，临时目录可能有不同的路径表示，使用resolve()来规范化
                assert service.work_dir.resolve() == Path(temp_dir).resolve()
                assert service.config["enabled"] is True

    def test_create_service_with_custom_config(self):
        """测试使用自定义配置创建服务"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = {
                "enabled": True,
                "update_interval": 10.0,
                "max_file_count": 5
            }
    
            with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
                service = VisualizationFactory.create_service(temp_dir, config)
    
                assert service is not None
                assert service.work_dir.resolve() == Path(temp_dir).resolve()
                assert service.config["update_interval"] == 10.0
                assert service.config["max_file_count"] == 5

    def test_create_service_invalid_work_dir(self):
        """测试无效工作目录的处理"""
        # 测试空字符串
        service = VisualizationFactory.create_service("")
        assert service is None

        # 测试None
        service = VisualizationFactory.create_service(None)
        assert service is None

        # 测试非字符串
        service = VisualizationFactory.create_service(123)
        assert service is None

    def test_create_service_folium_unavailable(self):
        """测试folium不可用时的处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch("src.visualization_service.FOLIUM_AVAILABLE", False):
                service = VisualizationFactory.create_service(temp_dir)
                assert service is None

    def test_create_service_import_error(self):
        """测试导入错误时的处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 模拟导入操作失败
            with patch(
                "builtins.__import__", side_effect=ImportError("Module not found")
            ):
                service = VisualizationFactory.create_service(temp_dir)
                assert service is None

    def test_create_service_directory_creation(self):
        """测试目录自动创建"""
        with tempfile.TemporaryDirectory() as temp_dir:
            subdir = os.path.join(temp_dir, "new_subdir")

            # 确保子目录不存在
            assert not os.path.exists(subdir)

            with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
                service = VisualizationFactory.create_service(subdir)

                # 目录应该被自动创建
                assert os.path.exists(subdir)
                assert service is not None

    def test_is_visualization_available_true(self):
        """测试可视化功能可用的情况"""
        with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
            result = VisualizationFactory.is_visualization_available()
            assert result is True

    def test_is_visualization_available_false(self):
        """测试可视化功能不可用的情况"""
        # 模拟 find_spec 返回 None，表示模块不存在
        with patch("importlib.util.find_spec", side_effect=[None, None]):
            result = VisualizationFactory.is_visualization_available()
            assert result is False

    def test_get_visualization_status_complete(self):
        """测试获取完整的可视化状态"""
        with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
            with patch("builtins.__import__", side_effect=None):
                status = VisualizationFactory.get_visualization_status()

                assert isinstance(status, dict)
                assert "available" in status
                assert "folium_available" in status
                assert "folium_version" in status
                assert "service_available" in status
                assert "work_dir_check" in status

                # 当依赖可用时，应该返回True
                assert status["available"] is True
                assert status["folium_available"] is True
                assert status["service_available"] is True

    def test_get_visualization_status_unavailable(self):
        """测试可视化不可用时的状态"""
        # 模拟 folium 导入失败
        with patch("builtins.__import__", side_effect=ImportError):
            # 模拟 find_spec 返回 None，表示 src.visualization_service 模块不存在
            with patch("importlib.util.find_spec", return_value=None):
                status = VisualizationFactory.get_visualization_status()

                assert status["available"] is False
                assert status["folium_available"] is False
                assert status["service_available"] is False

    def test_get_visualization_status_work_dir_check(self):
        """测试工作目录检查"""
        with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
            with patch("builtins.__import__", side_effect=None):
                # 检查当前工作目录应该是可写的
                status = VisualizationFactory.get_visualization_status()
                assert status["work_dir_check"] == "writable"

    def test_get_visualization_status_work_dir_error(self):
        """测试工作目录检查出错的情况"""
        with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
            with patch("builtins.__import__", side_effect=None):
                with patch(
                    "src.visualization_factory.os.getcwd",
                    side_effect=OSError("Mock error"),
                ):
                    status = VisualizationFactory.get_visualization_status()
                    assert status["work_dir_check"] == "error"


# 导入os模块用于测试
import os
