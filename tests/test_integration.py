"""
集成测试

为 Google Maps Grid Search 项目提供端到端的集成测试。
测试覆盖完整的搜索、中断和恢复流程。

基于 TECHNICAL_DESIGN.md 中的测试策略设计。
"""

import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch

from src.state_manager import StateManager
from src.data_models import create_search_orchestration
from src.app_state import ApplicationState
from src.api_client import APIClient
from src.config import ApplicationConfig


class TestIntegration(unittest.TestCase):
    """集成测试类"""

    def setUp(self):
        """测试前的设置"""
        # 创建临时工作目录
        self.temp_dir = tempfile.mkdtemp()
        self.work_dir = Path(self.temp_dir) / "test_work"
        self.work_dir.mkdir()

        # 创建模拟的命令行参数
        class MockArgs:
            def __init__(self, work_dir):
                self.work_dir = str(work_dir)
                self.dry_run = True
                self.test_area = None
                self.mock_seed = 42
                self.max_calls = 100
                self.visualize = False
                self.param_test = False
                self.place_type = "restaurant"
                self.location = "Berlin, Germany"
                self.log_level = "INFO"
                self.initial_radius = None
                self.initial_grid_step = None

        self.args = MockArgs(self.work_dir)

        # 创建模拟的算法配置
        self.algorithm_config = {
            "search": {
                "max_radius": 50000,
                "min_refinement_radius": 250,
                "max_refinement_levels": 4,
                "initial_radius_factor": 0.25,
                "grid_step_factor": 0.8,
                "subdivision_threshold": 45,
                "mini_radius_factor": 3.0,
                "mini_grid_overlap_factor": 1.0,
            },
            "api": {
                "nearby_search_single_page_size": 20,
                "nearby_search_actual_limit": 60,
            }
        }

        # 创建配置对象
        self.config = ApplicationConfig.from_args(
            self.args, 
            "test_key", 
            self.algorithm_config
        )

    def tearDown(self):
        """测试后的清理"""
        # 清理临时目录
        import shutil

        shutil.rmtree(self.temp_dir)

    def test_application_state_initialization(self):
        """测试应用程序状态初始化"""
        app_state = ApplicationState(self.config)

        # 验证状态对象创建成功
        self.assertIsNotNone(app_state.get_search_config())
        self.assertIsNotNone(app_state.get_api_state())
        self.assertIsNotNone(app_state.get_file_paths())
        self.assertIsNotNone(app_state.get_visualization_state())
        self.assertIsNotNone(app_state.get_search_state())

        # 验证配置正确
        self.assertTrue(app_state.get_search_config().dry_run)
        self.assertEqual(app_state.get_search_config().place_type, "restaurant")
        self.assertEqual(app_state.get_search_config().mock_seed, 42)

    def test_api_client_initialization(self):
        """测试API客户端初始化"""
        app_state = ApplicationState(self.config)

        api_client = APIClient(
            self.config,
            app_state.get_api_state(),
            app_state.get_search_config(),
            app_state.get_file_paths(),
        )

        # 验证API客户端创建成功
        self.assertIsNotNone(api_client)
        self.assertTrue(api_client.is_dry_run())
        self.assertEqual(api_client.get_max_calls(), 100)

    def test_state_manager_operations(self):
        """测试状态管理器操作"""
        # 创建状态管理器
        state_manager = StateManager(
            work_dir=str(self.work_dir), state_file="test_orchestration.json"
        )

        # 创建测试编排对象
        orchestration = create_search_orchestration(
            place_type="restaurant",
            location="Berlin, Germany",
            center_lat=52.5200,
            center_lng=13.4050,
            search_radius=1000.0,
        )

        # 测试保存和加载
        state_manager.save_state(orchestration)
        self.assertTrue(state_manager.state_file_exists())

        loaded_orchestration = state_manager.load_state()
        self.assertIsNotNone(loaded_orchestration)
        self.assertEqual(loaded_orchestration.place_type, "restaurant")
        self.assertEqual(loaded_orchestration.location, "Berlin, Germany")

    def test_atomic_operations(self):
        """测试原子性操作"""
        # 创建状态管理器
        state_manager = StateManager(
            work_dir=str(self.work_dir), state_file="test_atomic.json"
        )

        # 创建测试编排对象
        orchestration = create_search_orchestration(
            place_type="restaurant",
            location="Berlin, Germany",
            center_lat=52.5200,
            center_lng=13.4050,
            search_radius=1000.0,
        )

        # 测试原子性保存
        state_manager.save_state(orchestration)

        # 验证文件存在
        self.assertTrue(state_manager.state_file_exists())

        # 验证备份功能
        if state_manager.backup_enabled:
            state_manager.save_state(orchestration)  # 再次保存以创建备份
            self.assertTrue(state_manager.backup_file_exists())

    def test_interrupt_and_resume_workflow(self):
        """测试中断和恢复工作流程"""
        # 创建状态管理器
        state_manager = StateManager(
            work_dir=str(self.work_dir), state_file="test_interrupt.json"
        )

        # 创建初始编排对象
        orchestration = create_search_orchestration(
            place_type="restaurant",
            location="Berlin, Germany",
            center_lat=52.5200,
            center_lng=13.4050,
            search_radius=1000.0,
        )

        # 模拟部分工作完成 - 直接设置metrics
        orchestration.metrics["total_api_calls"] = 10
        orchestration.metrics["total_places_found"] = 5

        # 保存状态（模拟中断）
        state_manager.save_state(orchestration)

        # 恢复状态
        loaded_orchestration = state_manager.load_state_with_fallback()
        self.assertIsNotNone(loaded_orchestration)

        # 验证状态正确恢复
        self.assertEqual(loaded_orchestration.metrics.get("total_api_calls"), 10)
        self.assertEqual(loaded_orchestration.metrics.get("total_places_found"), 5)

    def test_full_workflow_simulation(self):
        """测试完整的工作流程模拟"""
        app_state = ApplicationState(self.config)

        # 创建状态管理器
        state_manager = StateManager(
            work_dir=app_state.get_file_paths().get_progress_dir(),
            state_file="test_workflow.json",
        )

        # 创建编排对象
        orchestration = create_search_orchestration(
            place_type="restaurant",
            location="Berlin, Germany",
            center_lat=52.5200,
            center_lng=13.4050,
            search_radius=1000.0,
        )

        # 模拟完整的搜索流程 - 直接设置metrics
        orchestration.metrics["total_api_calls"] = 25
        orchestration.metrics["total_places_found"] = 12

        # 保存最终状态
        state_manager.save_state(orchestration)

        # 验证状态保存成功
        self.assertTrue(state_manager.state_file_exists())

        # 验证状态信息
        state_info = state_manager.get_state_info()
        self.assertTrue(state_info["state_file_exists"])
        self.assertEqual(state_info["work_dir"], str(self.work_dir))

    def test_error_handling_integration(self):
        """测试错误处理的集成"""
        app_state = ApplicationState(self.config)

        # 创建API客户端
        api_client = APIClient(
            self.config,
            app_state.get_api_state(),
            app_state.get_search_config(),
            app_state.get_file_paths(),
        )

        # 测试地理编码搜索的错误处理
        with patch.object(api_client.api, "geocoding_search") as mock_geocoding:
            # 模拟API错误
            mock_geocoding.side_effect = Exception("API Error")

            # 验证异常抛出
            with self.assertRaises(Exception):
                api_client.geocoding_search("Invalid Location")

    def test_configuration_validation(self):
        """测试配置验证"""
        # 测试ApplicationConfig直接验证 - 空place_type
        from src.config import ApplicationConfig
        
        # 创建模拟的命令行参数
        class MockArgs:
            def __init__(self, place_type="", location="Berlin, Germany"):
                self.work_dir = "test_work"
                self.dry_run = True
                self.test_area = None
                self.mock_seed = 42
                self.max_calls = 100
                self.visualize = False
                self.param_test = False
                self.place_type = place_type
                self.location = location
                self.log_level = "INFO"
                self.initial_radius = None
                self.initial_grid_step = None

        algorithm_config = {
            "search": {
                "max_radius": 50000,
                "min_refinement_radius": 250,
                "max_refinement_levels": 4,
                "initial_radius_factor": 0.25,
                "grid_step_factor": 0.8,
                "subdivision_threshold": 45,
                "mini_radius_factor": 3.0,
                "mini_grid_overlap_factor": 1.0,
            },
            "api": {
                "nearby_search_single_page_size": 20,
                "nearby_search_actual_limit": 60,
            }
        }

        with self.assertRaises(ValueError) as context:
            config = ApplicationConfig.from_args(
                MockArgs(place_type=""),  # 无效的空place_type
                "test_key", 
                algorithm_config
            )
        self.assertIn("place_type cannot be empty", str(context.exception))

        # 测试ApplicationConfig直接验证 - 空target_location
        with self.assertRaises(ValueError) as context:
            config = ApplicationConfig.from_args(
                MockArgs("restaurant", ""),  # 无效的空target_location
                "test_key", 
                algorithm_config
            )
        self.assertIn("location cannot be empty", str(context.exception))


if __name__ == "__main__":
    unittest.main()
