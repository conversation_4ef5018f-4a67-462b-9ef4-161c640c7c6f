"""
可视化事件处理器模块测试

测试 VisualizationEventHandler 类的功能。
"""

from unittest.mock import Mock
from src.visualization_event_handler import (
    VisualizationEventHandler,
    VisualizationEvent,
)


class TestVisualizationEvent:
    """可视化事件测试类"""

    def test_event_initialization(self):
        """测试事件初始化"""
        event = VisualizationEvent(
            event_type="test_event",
            cell={"cell_id": "test_cell"},
            orchestration={"cells": {}},
        )

        assert event.event_type == "test_event"
        assert event.cell == {"cell_id": "test_cell"}
        assert event.orchestration == {"cells": {}}
        assert event.timestamp is not None
        assert event.metadata == {}

    def test_event_with_custom_metadata(self):
        """测试带自定义元数据的事件"""
        custom_metadata = {"key": "value", "number": 42}
        event = VisualizationEvent(event_type="custom_event", metadata=custom_metadata)

        assert event.metadata == custom_metadata
        assert event.metadata["key"] == "value"
        assert event.metadata["number"] == 42

    def test_event_with_custom_timestamp(self):
        """测试带自定义时间戳的事件"""
        custom_timestamp = 1234567890.0
        event = VisualizationEvent(
            event_type="timestamped_event", timestamp=custom_timestamp
        )

        assert event.timestamp == custom_timestamp


class TestVisualizationEventHandler:
    """可视化事件处理器测试类"""

    def test_initialization_with_service(self):
        """测试带服务的初始化"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        assert handler.visualization_service == mock_service
        assert handler.enabled is True
        assert "cell_processed" in handler.event_handlers
        assert "refinement_complete" in handler.event_handlers
        assert "initialization" in handler.event_handlers
        assert "error" in handler.event_handlers

    def test_initialization_without_service(self):
        """测试不带服务的初始化"""
        handler = VisualizationEventHandler(None)

        assert handler.visualization_service is None
        assert handler.enabled is False
        assert len(handler.event_handlers) == 0

    def test_register_custom_handler(self):
        """测试注册自定义事件处理器"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        custom_handler = Mock()
        handler.register_handler("custom_event", custom_handler)

        assert "custom_event" in handler.event_handlers
        assert handler.event_handlers["custom_event"] == custom_handler

    def test_unregister_handler(self):
        """测试注销事件处理器"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        # 注册一个自定义处理器
        custom_handler = Mock()
        handler.register_handler("custom_event", custom_handler)
        assert "custom_event" in handler.event_handlers

        # 注销处理器
        handler.unregister_handler("custom_event")
        assert "custom_event" not in handler.event_handlers

    def test_handle_event_enabled(self):
        """测试启用状态下的事件处理"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        # 模拟成功的事件处理
        result = handler.handle_event(
            "cell_processed", {"cell_id": "test"}, {"cells": {}}
        )

        assert result is True
        mock_service.update_on_cell_processed.assert_called_once()

    def test_handle_event_disabled(self):
        """测试禁用状态下的事件处理"""
        handler = VisualizationEventHandler(None)

        result = handler.handle_event(
            "cell_processed", {"cell_id": "test"}, {"cells": {}}
        )

        assert result is False

    def test_handle_event_invalid_type(self):
        """测试无效事件类型的处理"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        # 测试空字符串
        result = handler.handle_event("", {"cell_id": "test"}, {"cells": {}})
        assert result is False

        # 测试非字符串
        result = handler.handle_event(123, {"cell_id": "test"}, {"cells": {}})
        assert result is False

    def test_handle_event_unknown_type(self):
        """测试未知事件类型的处理"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        result = handler.handle_event(
            "unknown_event", {"cell_id": "test"}, {"cells": {}}
        )

        assert result is False

    def test_handle_event_with_exception(self):
        """测试事件处理过程中的异常处理"""
        mock_service = Mock()
        mock_service.update_on_cell_processed.side_effect = Exception("Mock error")

        handler = VisualizationEventHandler(mock_service)

        result = handler.handle_event(
            "cell_processed", {"cell_id": "test"}, {"cells": {}}
        )

        assert result is False

    def test_handle_callback_compatibility(self):
        """测试回调兼容性方法"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        result = handler.handle_callback(
            "cell_processed", {"cell_id": "test"}, {"cells": {}}
        )

        assert result is True
        mock_service.update_on_cell_processed.assert_called_once()

    def test_handle_cell_processed(self):
        """测试单元格处理完成事件"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        cell = Mock()
        cell.cell_id = "test_cell"
        orchestration = Mock()

        event = VisualizationEvent("cell_processed", cell, orchestration)
        handler._handle_cell_processed(event)

        mock_service.update_on_cell_processed.assert_called_once_with(
            cell, orchestration, force=False
        )

    def test_handle_cell_processed_with_force(self):
        """测试带强制参数的单元格处理事件"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        cell = Mock()
        cell.cell_id = "test_cell"
        orchestration = Mock()

        event = VisualizationEvent(
            "cell_processed", cell, orchestration, metadata={"force": True}
        )
        handler._handle_cell_processed(event)

        mock_service.update_on_cell_processed.assert_called_once_with(
            cell, orchestration, force=True
        )

    def test_handle_refinement_complete(self):
        """测试细化完成事件"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        orchestration = Mock()
        event = VisualizationEvent("refinement_complete", orchestration=orchestration)

        handler._handle_refinement_complete(event)

        mock_service.update_on_refinement.assert_called_once_with(orchestration)

    def test_handle_initialization(self):
        """测试初始化完成事件"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        orchestration = Mock()
        event = VisualizationEvent("initialization", orchestration=orchestration)

        handler._handle_initialization(event)

        mock_service.update_on_initialization.assert_called_once_with(orchestration)

    def test_handle_error(self):
        """测试错误事件处理"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        error = Exception("Test error")
        event = VisualizationEvent("error")

        handler._handle_error(event, error)

        # 错误应该被记录，但不应该抛出异常
        # 可以通过检查日志记录来验证（这里只验证不抛出异常）
        assert True  # 如果没有异常，测试通过

    def test_handle_error_with_service_error_recording(self):
        """测试带服务错误记录的错误处理"""
        mock_service = Mock()
        mock_service.record_error = Mock()

        handler = VisualizationEventHandler(mock_service)

        error = Exception("Test error")
        event = VisualizationEvent("error")

        handler._handle_error(event, error)

        mock_service.record_error.assert_called_once()

    def test_get_status(self):
        """测试获取状态信息"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        status = handler.get_status()

        assert isinstance(status, dict)
        assert "enabled" in status
        assert "registered_handlers" in status
        assert "service_available" in status
        assert status["enabled"] is True
        assert status["service_available"] is True
        assert len(status["registered_handlers"]) == 4  # 默认处理器数量

    def test_enable_handler(self):
        """测试启用处理器"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        # 先禁用
        handler.enabled = False
        assert handler.enabled is False

        # 启用
        handler.enable()
        assert handler.enabled is True

    def test_enable_handler_without_service(self):
        """测试无服务时的启用处理"""
        handler = VisualizationEventHandler(None)

        # 尝试启用，但没有服务
        handler.enable()
        assert handler.enabled is False  # 应该保持禁用状态

    def test_disable_handler(self):
        """测试禁用处理器"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        # 确保初始状态是启用的
        assert handler.enabled is True

        # 禁用
        handler.disable()
        assert handler.enabled is False

    def test_reset_handler(self):
        """测试重置处理器"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        # 添加一些自定义处理器
        custom_handler = Mock()
        handler.register_handler("custom_event", custom_handler)
        assert "custom_event" in handler.event_handlers

        # 重置
        handler.reset()

        # 自定义处理器应该被清除
        assert "custom_event" not in handler.event_handlers
        # 默认处理器应该被重新注册
        assert "cell_processed" in handler.event_handlers
        assert "refinement_complete" in handler.event_handlers
        assert "initialization" in handler.event_handlers
        assert "error" in handler.event_handlers

    def test_event_processing_with_missing_cell(self):
        """测试缺少单元格的事件处理"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        # 处理没有单元格的事件 - 应该仍然处理，只是传入None
        result = handler.handle_event("cell_processed", None, {"cells": {}})

        # 应该返回True，因为处理可以继续
        assert result is True

    def test_event_processing_with_missing_orchestration(self):
        """测试缺少编排对象的事件处理"""
        mock_service = Mock()
        handler = VisualizationEventHandler(mock_service)

        # 处理没有编排对象的事件 - 应该仍然处理，只是传入None
        result = handler.handle_event("cell_processed", {"cell_id": "test"}, None)

        # 应该返回True，因为处理可以继续
        assert result is True
