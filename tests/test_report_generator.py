"""
报告生成器模块单元测试

为 Google Maps Grid Search 项目的报告生成器模块提供单元测试。
测试覆盖从SearchOrchestration对象生成报告等核心功能。

基于 TECHNICAL_DESIGN.md 中的测试策略设计。
"""

import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock

from src.data_models import (
    SearchOrchestration,
    GridCell,
    SearchStatus,
    create_search_orchestration,
)
from src.report_generator import generate_report_from_orchestration


class TestReportGenerator(unittest.TestCase):
    """测试报告生成器模块"""

    def setUp(self):
        """测试前准备"""
        # 创建临时目录用于测试输出
        self.temp_dir = Path(tempfile.mkdtemp())

        # 创建测试编排对象
        self.test_orchestration = create_search_orchestration(
            place_type="restaurant",
            location="Berlin, Germany",
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )

        # 添加一些测试单元
        root_cell = self.test_orchestration.get_cell(
            self.test_orchestration.layers[0][0]
        )
        root_cell.update_status(SearchStatus.SEARCH_COMPLETE)
        root_cell.record_search_results(
            5, {"place1", "place2", "place3", "place4", "place5"}
        )

        # 添加一个需要细化的单元
        child_cell = GridCell(
            cell_id="L1-52.521000-13.406000",
            status=SearchStatus.REFINEMENT_NEEDED,
            layer_id=1,
            center_lat=52.521,
            center_lng=13.406,
            search_radius=500.0,
            parent_id=root_cell.cell_id,
        )
        self.test_orchestration.add_cell(child_cell)

        # 创建模拟的搜索配置
        self.mock_search_config = Mock()
        self.mock_search_config.target_location = "Berlin, Germany"
        self.mock_search_config.test_area = None
        self.mock_search_config.test_areas = {}
        self.mock_search_config.dry_run = False

        # 创建模拟的API状态
        self.mock_api_state = Mock()
        self.mock_api_state.api_call_count = 10

        # 创建模拟的文件路径
        self.mock_file_paths = Mock()
        self.mock_file_paths.output_file = str(self.temp_dir / "output.json")
        self.mock_file_paths.summary_csv = str(self.temp_dir / "summary.csv")
        self.mock_file_paths.detailed_data_dir = str(self.temp_dir / "detailed_data")

    def tearDown(self):
        """测试后清理"""
        # 清理临时目录
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_generate_report_from_orchestration(self):
        """测试从编排对象生成报告"""
        # 生成报告
        output_file = generate_report_from_orchestration(
            self.test_orchestration, str(self.temp_dir)
        )

        # 验证输出文件
        self.assertTrue(os.path.exists(output_file))
        self.assertTrue(output_file.endswith("output.json"))

        # 验证编排对象已保存
        saved_orchestration = SearchOrchestration.load_from_file(output_file)
        self.assertEqual(saved_orchestration.task_id, self.test_orchestration.task_id)
        self.assertEqual(
            saved_orchestration.place_type, self.test_orchestration.place_type
        )

    def test_generate_report_with_test_data(self):
        """测试从编排对象生成报告（包含测试数据）"""
        # 添加一些测试数据文件
        detailed_data_dir = self.temp_dir / "detailed_place_data"
        detailed_data_dir.mkdir(exist_ok=True)

        # 创建一些测试用的详细数据文件
        test_data = [
            {"place_id": "place1", "name": "Restaurant 1", "lat": 52.52, "lng": 13.405},
            {"place_id": "place2", "name": "Restaurant 2", "lat": 52.53, "lng": 13.415},
        ]

        for i, data in enumerate(test_data):
            with open(detailed_data_dir / f"place_{i}.json", "w") as f:
                import json

                json.dump(data, f)

        # 生成报告
        output_file = generate_report_from_orchestration(
            self.test_orchestration, str(self.temp_dir)
        )

        # 验证输出文件存在
        self.assertTrue(os.path.exists(output_file))


if __name__ == "__main__":
    unittest.main()
